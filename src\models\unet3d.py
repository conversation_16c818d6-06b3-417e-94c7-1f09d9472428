"""
3D U-Net implementation for physics prediction.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple


class Conv3DBlock(nn.Module):
    """3D Convolution block with BatchNorm and ReLU."""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3):
        super().__init__()
        padding = kernel_size // 2
        self.conv1 = nn.Conv3d(in_channels, out_channels, kernel_size, padding=padding)
        self.bn1 = nn.BatchNorm3d(out_channels)
        self.conv2 = nn.Conv3d(out_channels, out_channels, kernel_size, padding=padding)
        self.bn2 = nn.BatchNorm3d(out_channels)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))
        return x


class UNet3D(nn.Module):
    """3D U-Net for predicting next physics state."""
    
    def __init__(self, 
                 in_channels: int = 1,
                 out_channels: int = 1,
                 features: Tuple[int, ...] = (32, 64, 128, 256)):
        """
        Initialize 3D U-Net.
        
        Args:
            in_channels: number of input channels (1 for temperature field)
            out_channels: number of output channels (1 for temperature field)
            features: number of features at each level of the U-Net
        """
        super().__init__()
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.features = features
        
        # Encoder (downsampling path)
        self.encoder_blocks = nn.ModuleList()
        self.pool = nn.MaxPool3d(kernel_size=2, stride=2)
        
        # First encoder block
        self.encoder_blocks.append(Conv3DBlock(in_channels, features[0]))
        
        # Remaining encoder blocks
        for i in range(1, len(features)):
            self.encoder_blocks.append(Conv3DBlock(features[i-1], features[i]))
        
        # Bottleneck
        self.bottleneck = Conv3DBlock(features[-1], features[-1] * 2)
        
        # Decoder (upsampling path)
        self.decoder_blocks = nn.ModuleList()
        self.upconvs = nn.ModuleList()
        
        # Decoder blocks
        reversed_features = list(reversed(features))
        for i in range(len(reversed_features)):
            if i == 0:
                # First decoder block (from bottleneck)
                self.upconvs.append(
                    nn.ConvTranspose3d(features[-1] * 2, features[-1], kernel_size=2, stride=2)
                )
                self.decoder_blocks.append(Conv3DBlock(features[-1] * 2, features[-1]))
            else:
                # Remaining decoder blocks
                self.upconvs.append(
                    nn.ConvTranspose3d(reversed_features[i-1], reversed_features[i], kernel_size=2, stride=2)
                )
                self.decoder_blocks.append(Conv3DBlock(reversed_features[i] * 2, reversed_features[i]))
        
        # Final output layer
        self.final_conv = nn.Conv3d(features[0], out_channels, kernel_size=1)
    
    def forward(self, x):
        # Add channel dimension if needed
        if len(x.shape) == 4:  # (batch, depth, height, width)
            x = x.unsqueeze(1)  # (batch, 1, depth, height, width)
        
        # Encoder path
        encoder_features = []
        for i, encoder_block in enumerate(self.encoder_blocks):
            x = encoder_block(x)
            encoder_features.append(x)
            if i < len(self.encoder_blocks) - 1:  # Don't pool after last encoder block
                x = self.pool(x)
        
        # Bottleneck
        x = self.bottleneck(x)
        
        # Decoder path
        for i, (upconv, decoder_block) in enumerate(zip(self.upconvs, self.decoder_blocks)):
            x = upconv(x)
            
            # Get corresponding encoder feature
            encoder_feature = encoder_features[-(i+1)]
            
            # Handle size mismatch due to pooling/upsampling
            if x.shape != encoder_feature.shape:
                x = F.interpolate(x, size=encoder_feature.shape[2:], mode='trilinear', align_corners=False)
            
            # Concatenate with encoder feature (skip connection)
            x = torch.cat([x, encoder_feature], dim=1)
            x = decoder_block(x)
        
        # Final output
        x = self.final_conv(x)
        
        # Remove channel dimension if output is single channel
        if self.out_channels == 1:
            x = x.squeeze(1)
        
        return x


class PhysicsPredictor(nn.Module):
    """Wrapper for physics prediction with optional residual connection."""
    
    def __init__(self, 
                 grid_size: Tuple[int, int, int] = (32, 32, 32),
                 features: Tuple[int, ...] = (32, 64, 128, 256),
                 use_residual: bool = True):
        """
        Initialize physics predictor.
        
        Args:
            grid_size: dimensions of the simulation grid
            features: U-Net feature dimensions
            use_residual: whether to add residual connection (predict delta instead of absolute)
        """
        super().__init__()
        
        self.grid_size = grid_size
        self.use_residual = use_residual
        
        self.unet = UNet3D(in_channels=1, out_channels=1, features=features)
    
    def forward(self, x):
        """
        Predict next physics state.
        
        Args:
            x: current state tensor of shape (batch, depth, height, width)
            
        Returns:
            predicted next state
        """
        if self.use_residual:
            # Predict change (delta) and add to current state
            delta = self.unet(x)
            return x + delta
        else:
            # Predict absolute next state
            return self.unet(x)


def count_parameters(model):
    """Count the number of trainable parameters in a model."""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


if __name__ == "__main__":
    # Test the model
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    model = PhysicsPredictor().to(device)
    print(f"Model has {count_parameters(model):,} trainable parameters")
    
    # Test forward pass
    batch_size = 2
    test_input = torch.randn(batch_size, 32, 32, 32).to(device)
    
    with torch.no_grad():
        output = model(test_input)
        print(f"Input shape: {test_input.shape}")
        print(f"Output shape: {output.shape}")
        print("Model test passed!")
