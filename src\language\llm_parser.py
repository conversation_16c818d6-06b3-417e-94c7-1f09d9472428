"""
LLM-based Natural Language Parser for Physics Commands.

This module uses Large Language Models (OpenAI GPT, Anthropic Claude, etc.)
to parse complex natural language into structured physics commands.
"""

import json
import os
from typing import Dict, List, Any, Optional
import logging

from .parser import NLPParser, PhysicsCommand, CommandType
from ..physics.base import PhysicsDomain, Vector3D

# Optional imports for LLM providers
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

try:
    import anthropic
    HAS_ANTHROPIC = True
except ImportError:
    HAS_ANTHROPIC = False


class LLMParser(NLPParser):
    """LLM-based parser for sophisticated natural language understanding."""
    
    def __init__(self, 
                 provider: str = "openai",
                 model: str = None,
                 api_key: str = None):
        """
        Initialize LLM parser.
        
        Args:
            provider: "openai" or "anthropic"
            model: specific model name (e.g., "gpt-4", "claude-3-sonnet")
            api_key: API key (or set via environment variables)
        """
        super().__init__()
        
        self.provider = provider.lower()
        self.api_key = api_key
        
        # Set default models
        if model is None:
            if self.provider == "openai":
                self.model = "gpt-4"
            elif self.provider == "anthropic":
                self.model = "claude-3-sonnet-20240229"
            else:
                raise ValueError(f"Unsupported provider: {provider}")
        else:
            self.model = model
        
        # Initialize client
        self.client = None
        self._initialize_client()
        
        # System prompt for physics command parsing
        self.system_prompt = self._create_system_prompt()
    
    def _initialize_client(self):
        """Initialize the LLM client."""
        if self.provider == "openai":
            if not HAS_OPENAI:
                raise ImportError("OpenAI package not installed. Run: pip install openai")
            
            api_key = self.api_key or os.getenv("OPENAI_API_KEY")
            if not api_key:
                logging.warning("No OpenAI API key found. Set OPENAI_API_KEY environment variable.")
                return
            
            self.client = openai.OpenAI(api_key=api_key)
            
        elif self.provider == "anthropic":
            if not HAS_ANTHROPIC:
                raise ImportError("Anthropic package not installed. Run: pip install anthropic")
            
            api_key = self.api_key or os.getenv("ANTHROPIC_API_KEY")
            if not api_key:
                logging.warning("No Anthropic API key found. Set ANTHROPIC_API_KEY environment variable.")
                return
            
            self.client = anthropic.Anthropic(api_key=api_key)
    
    def _create_system_prompt(self) -> str:
        """Create system prompt for physics command parsing."""
        return """You are a physics simulation command parser. Your job is to convert natural language descriptions into structured JSON commands for a 3D physics simulation engine.

The simulation supports these physics domains:
- KINEMATICS: rigid body dynamics, gravity, collisions, terrain

Available command types:
- CREATE_OBJECT: create physics objects (balls, cubes, terrain, etc.)
- MODIFY_OBJECT: change object properties
- SET_PARAMETER: set global parameters (gravity, air resistance, etc.)
- RUN_SIMULATION: start/run simulation
- ANALYZE_DATA: extract analytics data
- RESET_SIMULATION: reset to initial state

For CREATE_OBJECT commands, you can create:
- Balls/spheres: specify mass, radius, position, material properties
- Hills/terrain: specify height, slope angle, material properties
- Cubes/boxes: specify mass, dimensions, position, material properties

Position can be specified as:
- Absolute coordinates: [x, y, z] in meters
- Relative positions: "center", "top", "left", "right" (will be converted to coordinates)

Material properties include:
- friction: 0.0 (slippery) to 1.0 (rough)
- restitution: 0.0 (no bounce) to 1.0 (perfect bounce)
- density: kg/m³

Example input: "create a sloped hill that's 10 meters high and place a 5kg ball at the top"

Example output:
```json
[
  {
    "command_type": "CREATE_OBJECT",
    "domain": "KINEMATICS",
    "action": "create_hill",
    "parameters": {
      "height": 10.0,
      "slope_angle": 30.0,
      "material": {"friction": 0.3, "restitution": 0.1}
    },
    "confidence": 0.9
  },
  {
    "command_type": "CREATE_OBJECT", 
    "domain": "KINEMATICS",
    "action": "create_ball",
    "parameters": {
      "mass": 5.0,
      "radius": 0.5,
      "position": [0, 10, 0],
      "material": {"friction": 0.4, "restitution": 0.6}
    },
    "confidence": 0.9
  }
]
```

Always respond with valid JSON. If you're unsure about a parameter, use reasonable defaults and set confidence < 0.8."""
    
    def parse(self, text: str) -> List[PhysicsCommand]:
        """Parse text using LLM."""
        if not self.client:
            logging.warning("LLM client not initialized. Falling back to keyword parser.")
            return self._fallback_parse(text)
        
        try:
            # Get LLM response
            response = self._query_llm(text)
            
            # Parse JSON response
            commands_data = json.loads(response)
            
            # Convert to PhysicsCommand objects
            commands = []
            for cmd_data in commands_data:
                command = PhysicsCommand(
                    command_type=CommandType(cmd_data['command_type']),
                    domain=PhysicsDomain(cmd_data['domain']),
                    action=cmd_data['action'],
                    parameters=cmd_data['parameters'],
                    confidence=cmd_data.get('confidence', 0.8),
                    raw_text=text
                )
                commands.append(command)
            
            return commands
            
        except Exception as e:
            logging.error(f"LLM parsing failed: {e}")
            return self._fallback_parse(text)
    
    def _query_llm(self, text: str) -> str:
        """Query the LLM with the given text."""
        if self.provider == "openai":
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": text}
                ],
                temperature=0.1,  # Low temperature for consistent parsing
                max_tokens=1000
            )
            return response.choices[0].message.content
            
        elif self.provider == "anthropic":
            response = self.client.messages.create(
                model=self.model,
                max_tokens=1000,
                temperature=0.1,
                system=self.system_prompt,
                messages=[
                    {"role": "user", "content": text}
                ]
            )
            return response.content[0].text
    
    def _fallback_parse(self, text: str) -> List[PhysicsCommand]:
        """Fallback to simple keyword parser if LLM fails."""
        from .parser import SimpleKeywordParser
        fallback_parser = SimpleKeywordParser()
        return fallback_parser.parse(text)


class HybridParser(NLPParser):
    """Hybrid parser that combines LLM and keyword-based approaches."""
    
    def __init__(self, 
                 llm_provider: str = "openai",
                 llm_model: str = None,
                 use_llm_fallback: bool = True):
        """
        Initialize hybrid parser.
        
        Args:
            llm_provider: LLM provider for complex parsing
            llm_model: specific LLM model
            use_llm_fallback: whether to use LLM when keyword parsing fails
        """
        super().__init__()
        
        # Initialize parsers
        from .parser import SimpleKeywordParser
        self.keyword_parser = SimpleKeywordParser()
        
        self.llm_parser = None
        self.use_llm_fallback = use_llm_fallback
        
        if use_llm_fallback:
            try:
                self.llm_parser = LLMParser(llm_provider, llm_model)
            except Exception as e:
                logging.warning(f"Failed to initialize LLM parser: {e}")
                self.use_llm_fallback = False
    
    def parse(self, text: str) -> List[PhysicsCommand]:
        """Parse using hybrid approach."""
        # First try keyword parser
        commands = self.keyword_parser.parse(text)
        
        # If keyword parser returns unknown command and LLM is available, try LLM
        if (self.use_llm_fallback and 
            len(commands) == 1 and 
            commands[0].command_type == CommandType.UNKNOWN and
            self.llm_parser):
            
            logging.info("Keyword parsing failed, trying LLM parser...")
            llm_commands = self.llm_parser.parse(text)
            
            # Use LLM results if they're better
            if llm_commands and llm_commands[0].command_type != CommandType.UNKNOWN:
                return llm_commands
        
        return commands


def create_parser(parser_type: str = "hybrid", **kwargs) -> NLPParser:
    """
    Factory function to create appropriate parser.
    
    Args:
        parser_type: "keyword", "llm", or "hybrid"
        **kwargs: additional arguments for parser initialization
    
    Returns:
        Configured parser instance
    """
    if parser_type == "keyword":
        from .parser import SimpleKeywordParser
        return SimpleKeywordParser()
    
    elif parser_type == "llm":
        return LLMParser(**kwargs)
    
    elif parser_type == "hybrid":
        return HybridParser(**kwargs)
    
    else:
        raise ValueError(f"Unknown parser type: {parser_type}")


# Example usage and testing
if __name__ == "__main__":
    # Test with different parsers
    test_commands = [
        "create a sloped hill that's 10 meters high and place a 5kg ball at the top",
        "set gravity to 9.8 m/s²",
        "run simulation for 5 seconds",
        "create a bouncy rubber ball with mass 2kg at position 5,0,5"
    ]
    
    # Test keyword parser
    print("=== Keyword Parser ===")
    from .parser import SimpleKeywordParser
    keyword_parser = SimpleKeywordParser()
    
    for cmd in test_commands:
        print(f"\nInput: {cmd}")
        results = keyword_parser.parse(cmd)
        for result in results:
            print(f"  {result.action}: {result.parameters}")
    
    # Test hybrid parser (will fallback to keyword if no LLM available)
    print("\n=== Hybrid Parser ===")
    hybrid_parser = create_parser("hybrid")
    
    for cmd in test_commands:
        print(f"\nInput: {cmd}")
        results = hybrid_parser.parse(cmd)
        for result in results:
            print(f"  {result.action}: {result.parameters} (confidence: {result.confidence})")
