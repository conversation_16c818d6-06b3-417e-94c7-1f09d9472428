#!/usr/bin/env python3
"""
Quick test for the full-screen UI without launching the web interface.
"""

import os
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.visualization.fullscreen_ui import FullScreenPhysicsUI
from src.core.simulation_engine import SimulationEngine
from src.physics.base import PhysicsDomain


def test_fullscreen_ui():
    """Test the full-screen UI creation without launching."""
    print("🧪 Testing Full-Screen UI Creation")
    print("=" * 40)
    
    # Create simulation engine
    engine = SimulationEngine(
        default_domain=PhysicsDomain.THERMODYNAMICS,
        parser_type="hybrid"
    )
    print("✅ Simulation engine created")
    
    # Create full-screen UI
    ui = FullScreenPhysicsUI(simulation_engine=engine)
    print("✅ Full-screen UI initialized")
    
    # Create interface (but don't launch)
    app = ui.create_interface()
    print("✅ Interface created successfully")
    
    # Check components
    expected_components = [
        'viewport', 'domain_dropdown', 'gravity_slider', 'time_step',
        'grid_size', 'play_btn', 'pause_btn', 'reset_btn', 'object_list',
        'command_input', 'execute_btn', 'timeline_slider'
    ]
    
    missing_components = []
    for component in expected_components:
        if component not in ui.ui_components:
            missing_components.append(component)
    
    if missing_components:
        print(f"❌ Missing components: {missing_components}")
    else:
        print("✅ All UI components created")
    
    # Test 3D scene creation
    try:
        fig = ui._create_3d_scene()
        print("✅ 3D scene creation works")
        
        # Check if origin is marked
        has_origin = any('Origin' in str(trace.name) for trace in fig.data)
        if has_origin:
            print("✅ Origin (0,0,0) is marked in scene")
        else:
            print("⚠️  Origin marker not found")
            
    except Exception as e:
        print(f"❌ 3D scene creation failed: {e}")
    
    print("\n🎉 Full-screen UI test completed!")
    return True


if __name__ == "__main__":
    test_fullscreen_ui()
