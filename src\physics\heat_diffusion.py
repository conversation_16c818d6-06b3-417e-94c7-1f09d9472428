"""
3D Heat Diffusion Solver using finite differences.

This module implements a simple 3D heat diffusion solver on a regular grid
using explicit finite difference methods.
"""

import numpy as np
from typing import Tuple, Optional


class HeatDiffusion3D:
    """3D Heat diffusion solver on a regular voxel grid."""
    
    def __init__(self, 
                 grid_size: Tuple[int, int, int] = (32, 32, 32),
                 dx: float = 1.0,
                 dt: float = 0.01,
                 alpha: float = 0.1):
        """
        Initialize the 3D heat diffusion solver.
        
        Args:
            grid_size: (nx, ny, nz) dimensions of the voxel grid
            dx: spatial step size (assuming uniform grid)
            dt: time step size
            alpha: thermal diffusivity coefficient
        """
        self.nx, self.ny, self.nz = grid_size
        self.dx = dx
        self.dt = dt
        self.alpha = alpha
        
        # Stability condition for explicit scheme
        self.stability_limit = dx**2 / (6 * alpha)
        if dt > self.stability_limit:
            print(f"Warning: dt={dt} > stability limit {self.stability_limit:.6f}")
        
        # Initialize temperature field
        self.temperature = np.zeros((self.nx, self.ny, self.nz), dtype=np.float32)
        
    def reset(self):
        """Reset temperature field to zero."""
        self.temperature.fill(0.0)
        
    def add_heat_source(self, 
                       center: Tuple[int, int, int], 
                       shape: str = "cube",
                       size: int = 3,
                       temperature: float = 100.0):
        """
        Add a heat source to the grid.
        
        Args:
            center: (x, y, z) center position of the heat source
            shape: "cube" or "sphere"
            size: radius/half-width of the heat source
            temperature: temperature value to set
        """
        cx, cy, cz = center
        
        if shape == "cube":
            x_min = max(0, cx - size)
            x_max = min(self.nx, cx + size + 1)
            y_min = max(0, cy - size)
            y_max = min(self.ny, cy + size + 1)
            z_min = max(0, cz - size)
            z_max = min(self.nz, cz + size + 1)
            
            self.temperature[x_min:x_max, y_min:y_max, z_min:z_max] = temperature
            
        elif shape == "sphere":
            for i in range(max(0, cx - size), min(self.nx, cx + size + 1)):
                for j in range(max(0, cy - size), min(self.ny, cy + size + 1)):
                    for k in range(max(0, cz - size), min(self.nz, cz + size + 1)):
                        dist_sq = (i - cx)**2 + (j - cy)**2 + (k - cz)**2
                        if dist_sq <= size**2:
                            self.temperature[i, j, k] = temperature
    
    def step(self) -> np.ndarray:
        """
        Perform one time step of heat diffusion using explicit finite differences.
        
        Returns:
            Updated temperature field
        """
        # Create padded array for boundary conditions (zero temperature at boundaries)
        temp_padded = np.pad(self.temperature, 1, mode='constant', constant_values=0)
        
        # Compute Laplacian using finite differences
        laplacian = (
            temp_padded[2:, 1:-1, 1:-1] + temp_padded[:-2, 1:-1, 1:-1] +  # x direction
            temp_padded[1:-1, 2:, 1:-1] + temp_padded[1:-1, :-2, 1:-1] +  # y direction
            temp_padded[1:-1, 1:-1, 2:] + temp_padded[1:-1, 1:-1, :-2] -  # z direction
            6 * temp_padded[1:-1, 1:-1, 1:-1]  # center term
        ) / (self.dx**2)
        
        # Update temperature using explicit Euler method
        self.temperature += self.alpha * self.dt * laplacian
        
        return self.temperature.copy()
    
    def simulate(self, n_steps: int) -> np.ndarray:
        """
        Run simulation for multiple time steps.
        
        Args:
            n_steps: number of time steps to simulate
            
        Returns:
            Array of shape (n_steps+1, nx, ny, nz) containing temperature evolution
        """
        history = np.zeros((n_steps + 1, self.nx, self.ny, self.nz), dtype=np.float32)
        history[0] = self.temperature.copy()
        
        for i in range(n_steps):
            self.step()
            history[i + 1] = self.temperature.copy()
            
        return history
    
    def get_state(self) -> np.ndarray:
        """Get current temperature field."""
        return self.temperature.copy()
    
    def set_state(self, temperature: np.ndarray):
        """Set temperature field."""
        assert temperature.shape == (self.nx, self.ny, self.nz), \
            f"Temperature shape {temperature.shape} doesn't match grid {(self.nx, self.ny, self.nz)}"
        self.temperature = temperature.astype(np.float32)


def create_random_initial_condition(grid_size: Tuple[int, int, int] = (32, 32, 32),
                                  n_sources: int = 3,
                                  temp_range: Tuple[float, float] = (50.0, 150.0),
                                  size_range: Tuple[int, int] = (2, 5)) -> np.ndarray:
    """
    Create a random initial condition with hot/cold sources.
    
    Args:
        grid_size: dimensions of the grid
        n_sources: number of heat sources to place
        temp_range: (min_temp, max_temp) range for source temperatures
        size_range: (min_size, max_size) range for source sizes
        
    Returns:
        Initial temperature field
    """
    solver = HeatDiffusion3D(grid_size)
    nx, ny, nz = grid_size
    
    for _ in range(n_sources):
        # Random position
        cx = np.random.randint(5, nx - 5)
        cy = np.random.randint(5, ny - 5)
        cz = np.random.randint(5, nz - 5)
        
        # Random properties
        shape = np.random.choice(["cube", "sphere"])
        size = np.random.randint(size_range[0], size_range[1] + 1)
        temp = np.random.uniform(temp_range[0], temp_range[1])
        
        solver.add_heat_source((cx, cy, cz), shape, size, temp)
    
    return solver.get_state()
