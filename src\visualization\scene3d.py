"""
3D Scene Management System for Physics Visualization.

This module provides a Blender-like 3D scene system with objects, materials,
lighting, and camera controls for visualizing physics simulations.
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import uuid

from ..physics.base import Vector3D, Transform3D, PhysicsObject


class LightType(Enum):
    """Types of lights in the scene."""
    DIRECTIONAL = "directional"
    POINT = "point"
    SPOT = "spot"
    AMBIENT = "ambient"


class MaterialType(Enum):
    """Types of materials for rendering."""
    BASIC = "basic"
    PHONG = "phong"
    PBR = "pbr"  # Physically Based Rendering
    WIREFRAME = "wireframe"


@dataclass
class Color:
    """RGB color with alpha channel."""
    r: float = 1.0
    g: float = 1.0
    b: float = 1.0
    a: float = 1.0
    
    def to_hex(self) -> str:
        """Convert to hex color string."""
        r = int(self.r * 255)
        g = int(self.g * 255)
        b = int(self.b * 255)
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def to_array(self) -> np.ndarray:
        """Convert to numpy array."""
        return np.array([self.r, self.g, self.b, self.a])
    
    @classmethod
    def from_hex(cls, hex_color: str):
        """Create color from hex string."""
        hex_color = hex_color.lstrip('#')
        r = int(hex_color[0:2], 16) / 255.0
        g = int(hex_color[2:4], 16) / 255.0
        b = int(hex_color[4:6], 16) / 255.0
        return cls(r, g, b)


@dataclass
class Material3D:
    """3D material properties for rendering."""
    name: str = "default"
    material_type: MaterialType = MaterialType.PHONG
    
    # Basic properties
    color: Color = field(default_factory=lambda: Color(0.8, 0.8, 0.8))
    opacity: float = 1.0
    
    # Phong/PBR properties
    metallic: float = 0.0
    roughness: float = 0.5
    specular: float = 0.5
    shininess: float = 32.0
    
    # Texture properties
    texture_path: Optional[str] = None
    normal_map_path: Optional[str] = None
    
    # Physics visualization
    show_wireframe: bool = False
    wireframe_color: Color = field(default_factory=lambda: Color(0, 0, 0))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'name': self.name,
            'type': self.material_type.value,
            'color': [self.color.r, self.color.g, self.color.b, self.color.a],
            'opacity': self.opacity,
            'metallic': self.metallic,
            'roughness': self.roughness,
            'specular': self.specular,
            'shininess': self.shininess,
            'show_wireframe': self.show_wireframe,
            'wireframe_color': [self.wireframe_color.r, self.wireframe_color.g, self.wireframe_color.b]
        }


class Mesh:
    """3D mesh data."""
    
    def __init__(self, 
                 vertices: np.ndarray,
                 faces: np.ndarray,
                 normals: Optional[np.ndarray] = None,
                 uvs: Optional[np.ndarray] = None):
        """
        Initialize mesh.
        
        Args:
            vertices: array of shape (n_vertices, 3)
            faces: array of shape (n_faces, 3) with vertex indices
            normals: array of shape (n_vertices, 3) with vertex normals
            uvs: array of shape (n_vertices, 2) with texture coordinates
        """
        self.vertices = vertices
        self.faces = faces
        self.normals = normals
        self.uvs = uvs
        
        # Compute normals if not provided
        if self.normals is None:
            self.normals = self._compute_normals()
    
    def _compute_normals(self) -> np.ndarray:
        """Compute vertex normals from face data."""
        normals = np.zeros_like(self.vertices)
        
        for face in self.faces:
            v0, v1, v2 = self.vertices[face]
            
            # Compute face normal
            edge1 = v1 - v0
            edge2 = v2 - v0
            face_normal = np.cross(edge1, edge2)
            face_normal = face_normal / np.linalg.norm(face_normal)
            
            # Add to vertex normals
            normals[face] += face_normal
        
        # Normalize vertex normals
        norms = np.linalg.norm(normals, axis=1, keepdims=True)
        norms[norms == 0] = 1  # Avoid division by zero
        normals = normals / norms
        
        return normals
    
    @classmethod
    def create_sphere(cls, radius: float = 1.0, subdivisions: int = 2):
        """Create a sphere mesh."""
        # Start with icosahedron
        phi = (1 + np.sqrt(5)) / 2  # Golden ratio
        
        # Icosahedron vertices
        vertices = np.array([
            [-1, phi, 0], [1, phi, 0], [-1, -phi, 0], [1, -phi, 0],
            [0, -1, phi], [0, 1, phi], [0, -1, -phi], [0, 1, -phi],
            [phi, 0, -1], [phi, 0, 1], [-phi, 0, -1], [-phi, 0, 1]
        ], dtype=float)
        
        # Normalize to unit sphere
        vertices = vertices / np.linalg.norm(vertices, axis=1, keepdims=True)
        
        # Icosahedron faces
        faces = np.array([
            [0, 11, 5], [0, 5, 1], [0, 1, 7], [0, 7, 10], [0, 10, 11],
            [1, 5, 9], [5, 11, 4], [11, 10, 2], [10, 7, 6], [7, 1, 8],
            [3, 9, 4], [3, 4, 2], [3, 2, 6], [3, 6, 8], [3, 8, 9],
            [4, 9, 5], [2, 4, 11], [6, 2, 10], [8, 6, 7], [9, 8, 1]
        ])
        
        # Subdivide for smoother sphere
        for _ in range(subdivisions):
            vertices, faces = cls._subdivide_mesh(vertices, faces)
            # Project to sphere
            vertices = vertices / np.linalg.norm(vertices, axis=1, keepdims=True)
        
        # Scale by radius
        vertices *= radius
        
        return cls(vertices, faces)
    
    @classmethod
    def create_cube(cls, size: Vector3D = Vector3D(1, 1, 1)):
        """Create a cube mesh."""
        sx, sy, sz = size.x / 2, size.y / 2, size.z / 2
        
        vertices = np.array([
            [-sx, -sy, -sz], [sx, -sy, -sz], [sx, sy, -sz], [-sx, sy, -sz],  # Back face
            [-sx, -sy, sz], [sx, -sy, sz], [sx, sy, sz], [-sx, sy, sz]       # Front face
        ])
        
        faces = np.array([
            [0, 1, 2], [0, 2, 3],  # Back
            [4, 7, 6], [4, 6, 5],  # Front
            [0, 4, 5], [0, 5, 1],  # Bottom
            [2, 6, 7], [2, 7, 3],  # Top
            [0, 3, 7], [0, 7, 4],  # Left
            [1, 5, 6], [1, 6, 2]   # Right
        ])
        
        return cls(vertices, faces)
    
    @classmethod
    def create_plane(cls, size: Vector3D = Vector3D(10, 1, 10), subdivisions: int = 10):
        """Create a plane mesh for terrain."""
        sx, sz = size.x / 2, size.z / 2
        
        # Create grid of vertices
        x = np.linspace(-sx, sx, subdivisions + 1)
        z = np.linspace(-sz, sz, subdivisions + 1)
        X, Z = np.meshgrid(x, z)
        Y = np.zeros_like(X)
        
        vertices = np.column_stack([X.ravel(), Y.ravel(), Z.ravel()])
        
        # Create faces
        faces = []
        for i in range(subdivisions):
            for j in range(subdivisions):
                # Two triangles per quad
                v0 = i * (subdivisions + 1) + j
                v1 = v0 + 1
                v2 = v0 + subdivisions + 1
                v3 = v2 + 1
                
                faces.extend([[v0, v1, v2], [v1, v3, v2]])
        
        faces = np.array(faces)
        
        return cls(vertices, faces)
    
    @staticmethod
    def _subdivide_mesh(vertices: np.ndarray, faces: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Subdivide mesh by adding midpoint vertices."""
        # This is a simplified subdivision - real implementation would be more complex
        new_vertices = vertices.copy()
        new_faces = []
        
        for face in faces:
            v0, v1, v2 = vertices[face]
            
            # Add midpoint vertices
            mid01 = (v0 + v1) / 2
            mid12 = (v1 + v2) / 2
            mid20 = (v2 + v0) / 2
            
            # Add to vertex list
            idx_mid01 = len(new_vertices)
            idx_mid12 = idx_mid01 + 1
            idx_mid20 = idx_mid01 + 2
            
            new_vertices = np.vstack([new_vertices, mid01, mid12, mid20])
            
            # Create 4 new faces
            new_faces.extend([
                [face[0], idx_mid01, idx_mid20],
                [face[1], idx_mid12, idx_mid01],
                [face[2], idx_mid20, idx_mid12],
                [idx_mid01, idx_mid12, idx_mid20]
            ])
        
        return new_vertices, np.array(new_faces)


class SceneObject:
    """3D object in the scene."""
    
    def __init__(self,
                 name: str,
                 mesh: Mesh,
                 material: Material3D = None,
                 transform: Transform3D = None,
                 physics_object: PhysicsObject = None):
        """
        Initialize scene object.
        
        Args:
            name: object name
            mesh: 3D mesh data
            material: rendering material
            transform: 3D transformation
            physics_object: associated physics object
        """
        self.id = str(uuid.uuid4())
        self.name = name
        self.mesh = mesh
        self.material = material or Material3D()
        self.transform = transform or Transform3D()
        self.physics_object = physics_object
        
        self.visible = True
        self.cast_shadows = True
        self.receive_shadows = True
        
        # Animation/keyframe data
        self.keyframes: Dict[float, Transform3D] = {}
    
    def update_from_physics(self):
        """Update transform from associated physics object."""
        if self.physics_object:
            self.transform.position = self.physics_object.get_position()
    
    def set_keyframe(self, time: float, transform: Transform3D = None):
        """Set keyframe for animation."""
        if transform is None:
            transform = self.transform
        self.keyframes[time] = transform
    
    def get_transform_at_time(self, time: float) -> Transform3D:
        """Get interpolated transform at given time."""
        if not self.keyframes:
            return self.transform
        
        # Find surrounding keyframes
        times = sorted(self.keyframes.keys())
        
        if time <= times[0]:
            return self.keyframes[times[0]]
        if time >= times[-1]:
            return self.keyframes[times[-1]]
        
        # Linear interpolation between keyframes
        for i in range(len(times) - 1):
            if times[i] <= time <= times[i + 1]:
                t0, t1 = times[i], times[i + 1]
                transform0 = self.keyframes[t0]
                transform1 = self.keyframes[t1]
                
                # Interpolation factor
                alpha = (time - t0) / (t1 - t0)
                
                # Interpolate position
                pos = transform0.position + (transform1.position - transform0.position) * alpha
                
                # Interpolate rotation (simplified)
                rot = transform0.rotation + (transform1.rotation - transform0.rotation) * alpha
                
                # Interpolate scale
                scale = transform0.scale + (transform1.scale - transform0.scale) * alpha
                
                return Transform3D(position=pos, rotation=rot, scale=scale)
        
        return self.transform


class Camera:
    """3D camera for viewing the scene."""
    
    def __init__(self,
                 position: Vector3D = Vector3D(0, 5, 10),
                 target: Vector3D = Vector3D(0, 0, 0),
                 up: Vector3D = Vector3D(0, 1, 0),
                 fov: float = 45.0,
                 near: float = 0.1,
                 far: float = 1000.0):
        """
        Initialize camera.
        
        Args:
            position: camera position
            target: look-at target
            up: up vector
            fov: field of view in degrees
            near: near clipping plane
            far: far clipping plane
        """
        self.position = position
        self.target = target
        self.up = up
        self.fov = fov
        self.near = near
        self.far = far
        
        # Camera controls
        self.orbit_distance = (target - position).magnitude()
        self.orbit_angles = Vector3D(0, 0, 0)  # phi, theta, radius
    
    def orbit(self, delta_phi: float, delta_theta: float):
        """Orbit camera around target."""
        self.orbit_angles.x += delta_phi
        self.orbit_angles.y += delta_theta
        
        # Clamp theta to avoid gimbal lock
        self.orbit_angles.y = max(-np.pi/2 + 0.1, min(np.pi/2 - 0.1, self.orbit_angles.y))
        
        # Update position
        phi, theta = self.orbit_angles.x, self.orbit_angles.y
        x = self.orbit_distance * np.cos(theta) * np.cos(phi)
        y = self.orbit_distance * np.sin(theta)
        z = self.orbit_distance * np.cos(theta) * np.sin(phi)
        
        self.position = self.target + Vector3D(x, y, z)
    
    def zoom(self, delta: float):
        """Zoom camera in/out."""
        self.orbit_distance *= (1 + delta)
        self.orbit_distance = max(0.1, min(100, self.orbit_distance))
        
        # Update position
        direction = (self.position - self.target).normalize()
        self.position = self.target + direction * self.orbit_distance
    
    def pan(self, delta_x: float, delta_y: float):
        """Pan camera."""
        # Calculate right and up vectors
        forward = (self.target - self.position).normalize()
        right = Vector3D(
            forward.y * self.up.z - forward.z * self.up.y,
            forward.z * self.up.x - forward.x * self.up.z,
            forward.x * self.up.y - forward.y * self.up.x
        ).normalize()
        up = Vector3D(
            right.y * forward.z - right.z * forward.y,
            right.z * forward.x - right.x * forward.z,
            right.x * forward.y - right.y * forward.x
        ).normalize()
        
        # Move camera and target
        movement = right * delta_x + up * delta_y
        self.position = self.position + movement
        self.target = self.target + movement


class Light:
    """3D light source."""
    
    def __init__(self,
                 light_type: LightType,
                 position: Vector3D = Vector3D(0, 10, 0),
                 direction: Vector3D = Vector3D(0, -1, 0),
                 color: Color = Color(1, 1, 1),
                 intensity: float = 1.0):
        """
        Initialize light.
        
        Args:
            light_type: type of light
            position: light position (for point/spot lights)
            direction: light direction (for directional/spot lights)
            color: light color
            intensity: light intensity
        """
        self.id = str(uuid.uuid4())
        self.light_type = light_type
        self.position = position
        self.direction = direction.normalize()
        self.color = color
        self.intensity = intensity
        
        # Spot light properties
        self.cone_angle = 30.0  # degrees
        self.penumbra = 10.0    # degrees
        
        # Attenuation for point/spot lights
        self.constant_attenuation = 1.0
        self.linear_attenuation = 0.1
        self.quadratic_attenuation = 0.01


class Scene3D:
    """3D scene container."""
    
    def __init__(self, name: str = "Physics Scene"):
        """Initialize 3D scene."""
        self.name = name
        self.objects: Dict[str, SceneObject] = {}
        self.lights: Dict[str, Light] = {}
        self.camera = Camera()
        
        # Scene properties
        self.background_color = Color(0.2, 0.2, 0.3)
        self.ambient_light = Color(0.3, 0.3, 0.3)
        
        # Animation
        self.current_time = 0.0
        self.animation_duration = 10.0
        self.fps = 60
        
        # Add default lighting
        self._setup_default_lighting()
    
    def _setup_default_lighting(self):
        """Setup default scene lighting."""
        # Main directional light (sun)
        main_light = Light(
            LightType.DIRECTIONAL,
            direction=Vector3D(-0.5, -1, -0.3),
            color=Color(1, 1, 0.9),
            intensity=0.8
        )
        self.add_light(main_light, "main_light")
        
        # Fill light
        fill_light = Light(
            LightType.DIRECTIONAL,
            direction=Vector3D(0.3, -0.5, 0.8),
            color=Color(0.7, 0.8, 1),
            intensity=0.3
        )
        self.add_light(fill_light, "fill_light")
    
    def add_object(self, obj: SceneObject, name: str = None):
        """Add object to scene."""
        if name is None:
            name = obj.name
        self.objects[name] = obj
    
    def remove_object(self, name: str):
        """Remove object from scene."""
        if name in self.objects:
            del self.objects[name]
    
    def get_object(self, name: str) -> Optional[SceneObject]:
        """Get object by name."""
        return self.objects.get(name)
    
    def add_light(self, light: Light, name: str):
        """Add light to scene."""
        self.lights[name] = light
    
    def remove_light(self, name: str):
        """Remove light from scene."""
        if name in self.lights:
            del self.lights[name]
    
    def update(self, dt: float):
        """Update scene for one frame."""
        self.current_time += dt
        
        # Update objects from physics
        for obj in self.objects.values():
            obj.update_from_physics()
    
    def create_physics_object_mesh(self, physics_obj: PhysicsObject) -> SceneObject:
        """Create scene object from physics object."""
        from ..physics.kinematics import RigidBody, Terrain
        
        if isinstance(physics_obj, RigidBody):
            return self._create_rigid_body_mesh(physics_obj)
        elif isinstance(physics_obj, Terrain):
            return self._create_terrain_mesh(physics_obj)
        else:
            # Default sphere for unknown objects
            mesh = Mesh.create_sphere(0.5)
            material = Material3D(color=Color(0.8, 0.8, 0.8))
            return SceneObject(physics_obj.name, mesh, material, physics_obj.transform, physics_obj)
    
    def _create_rigid_body_mesh(self, body) -> SceneObject:
        """Create mesh for rigid body."""
        if body.shape == "sphere":
            mesh = Mesh.create_sphere(body.size.x)
            color = Color(0.8, 0.3, 0.3)  # Red for balls
        elif body.shape == "cube" or body.shape == "box":
            mesh = Mesh.create_cube(body.size)
            color = Color(0.3, 0.8, 0.3)  # Green for cubes
        else:
            mesh = Mesh.create_sphere(0.5)
            color = Color(0.8, 0.8, 0.8)  # Gray for unknown
        
        material = Material3D(color=color)
        return SceneObject(body.name, mesh, material, body.transform, body)
    
    def _create_terrain_mesh(self, terrain) -> SceneObject:
        """Create mesh for terrain."""
        mesh = Mesh.create_plane(Vector3D(20, 1, 20), 20)
        
        # Apply height function to vertices
        for i, vertex in enumerate(mesh.vertices):
            x, y, z = vertex
            new_y = terrain.get_height_at(x, z)
            mesh.vertices[i][1] = new_y
        
        # Recompute normals
        mesh.normals = mesh._compute_normals()
        
        material = Material3D(
            color=Color(0.6, 0.4, 0.2),  # Brown for terrain
            roughness=0.8
        )
        
        return SceneObject(terrain.name, mesh, material, terrain.transform, terrain)
