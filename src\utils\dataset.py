"""
Dataset generation utilities for training the 3D physics model.
"""

import numpy as np
import h5py
import os
from typing import <PERSON><PERSON>, List, Optional
from tqdm import tqdm

from ..physics.heat_diffusion import HeatDiffusion3D, create_random_initial_condition


class PhysicsDataset:
    """Dataset generator for 3D physics simulations."""
    
    def __init__(self, 
                 grid_size: Tuple[int, int, int] = (32, 32, 32),
                 dt: float = 0.01,
                 alpha: float = 0.1):
        """
        Initialize dataset generator.
        
        Args:
            grid_size: dimensions of the simulation grid
            dt: time step size
            alpha: thermal diffusivity
        """
        self.grid_size = grid_size
        self.dt = dt
        self.alpha = alpha
        self.solver = HeatDiffusion3D(grid_size, dt=dt, alpha=alpha)
    
    def generate_sequence(self, 
                         n_steps: int = 50,
                         n_sources: int = None) -> np.ndarray:
        """
        Generate a single simulation sequence.
        
        Args:
            n_steps: number of time steps to simulate
            n_sources: number of initial heat sources (random if None)
            
        Returns:
            Array of shape (n_steps+1, nx, ny, nz) with temperature evolution
        """
        # Reset solver
        self.solver.reset()
        
        # Generate random initial condition
        if n_sources is None:
            n_sources = np.random.randint(1, 5)
        
        initial_temp = create_random_initial_condition(
            self.grid_size, 
            n_sources=n_sources,
            temp_range=(50.0, 150.0),
            size_range=(2, 5)
        )
        
        self.solver.set_state(initial_temp)
        
        # Run simulation
        return self.solver.simulate(n_steps)
    
    def generate_training_pairs(self, 
                              n_sequences: int = 1000,
                              n_steps: int = 50,
                              save_path: Optional[str] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate training pairs (current_state, next_state) for supervised learning.
        
        Args:
            n_sequences: number of simulation sequences to generate
            n_steps: number of time steps per sequence
            save_path: path to save the dataset (optional)
            
        Returns:
            (inputs, targets) arrays for training
        """
        print(f"Generating {n_sequences} sequences with {n_steps} steps each...")
        
        all_inputs = []
        all_targets = []
        
        for i in tqdm(range(n_sequences), desc="Generating sequences"):
            sequence = self.generate_sequence(n_steps)
            
            # Create input-target pairs from consecutive time steps
            inputs = sequence[:-1]  # t=0 to t=n_steps-1
            targets = sequence[1:]  # t=1 to t=n_steps
            
            all_inputs.append(inputs)
            all_targets.append(targets)
        
        # Concatenate all sequences
        inputs = np.concatenate(all_inputs, axis=0)
        targets = np.concatenate(all_targets, axis=0)
        
        print(f"Generated {len(inputs)} training pairs")
        print(f"Input shape: {inputs.shape}")
        print(f"Target shape: {targets.shape}")
        
        # Save dataset if path provided
        if save_path:
            self.save_dataset(inputs, targets, save_path)
        
        return inputs, targets
    
    def save_dataset(self, 
                    inputs: np.ndarray, 
                    targets: np.ndarray, 
                    save_path: str):
        """Save dataset to HDF5 file."""
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        with h5py.File(save_path, 'w') as f:
            f.create_dataset('inputs', data=inputs, compression='gzip')
            f.create_dataset('targets', data=targets, compression='gzip')
            
            # Save metadata
            f.attrs['grid_size'] = self.grid_size
            f.attrs['dt'] = self.dt
            f.attrs['alpha'] = self.alpha
            f.attrs['n_samples'] = len(inputs)
        
        print(f"Dataset saved to {save_path}")
    
    @staticmethod
    def load_dataset(load_path: str) -> Tuple[np.ndarray, np.ndarray, dict]:
        """
        Load dataset from HDF5 file.
        
        Returns:
            (inputs, targets, metadata)
        """
        with h5py.File(load_path, 'r') as f:
            inputs = f['inputs'][:]
            targets = f['targets'][:]
            
            metadata = {
                'grid_size': tuple(f.attrs['grid_size']),
                'dt': f.attrs['dt'],
                'alpha': f.attrs['alpha'],
                'n_samples': f.attrs['n_samples']
            }
        
        return inputs, targets, metadata


def create_demo_dataset(save_dir: str = "data", 
                       n_sequences: int = 100,
                       n_steps: int = 30):
    """Create a small demo dataset for testing."""
    dataset_gen = PhysicsDataset()
    
    save_path = os.path.join(save_dir, "demo_dataset.h5")
    inputs, targets = dataset_gen.generate_training_pairs(
        n_sequences=n_sequences,
        n_steps=n_steps,
        save_path=save_path
    )
    
    return inputs, targets


if __name__ == "__main__":
    # Generate demo dataset
    create_demo_dataset()
