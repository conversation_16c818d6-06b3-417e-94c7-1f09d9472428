2025-08-16 18:04:16,241 - root - WARNING - Failed to initialize LLM parser: OpenAI package not installed. Run: pip install openai
2025-08-16 18:04:16,259 - root - INFO - Initialized PhysicsDomain.KINEMATICS physics engine
2025-08-16 18:04:16,259 - root - INFO - SimulationEngine initialized with domain: PhysicsDomain.KINEMATICS
2025-08-16 18:04:16,319 - root - ERROR - Application error: 'numpy.ndarray' object has no attribute 'to_array'
2025-08-16 18:04:53,073 - root - WARNING - Failed to initialize LLM parser: OpenAI package not installed. Run: pip install openai
2025-08-16 18:04:53,074 - root - INFO - Initialized PhysicsDomain.KINEMATICS physics engine
2025-08-16 18:04:53,074 - root - INFO - SimulationEngine initialized with domain: PhysicsDomain.KINEMATICS
2025-08-16 18:04:53,144 - root - INFO - Started simulation (duration: 5.0)
2025-08-16 18:04:58,152 - root - INFO - Simulation stopped
2025-08-16 18:08:56,765 - root - WARNING - Failed to initialize LLM parser: OpenAI package not installed. Run: pip install openai
2025-08-16 18:08:56,765 - root - INFO - Initialized PhysicsDomain.KINEMATICS physics engine
2025-08-16 18:08:56,765 - root - INFO - SimulationEngine initialized with domain: PhysicsDomain.KINEMATICS
2025-08-16 18:08:56,826 - root - INFO - Started simulation (duration: 5.0)
2025-08-16 18:09:01,834 - root - INFO - Simulation stopped
2025-08-16 18:09:40,880 - root - WARNING - Failed to initialize LLM parser: OpenAI package not installed. Run: pip install openai
2025-08-16 18:09:40,880 - root - INFO - Initialized PhysicsDomain.KINEMATICS physics engine
2025-08-16 18:09:40,880 - root - INFO - SimulationEngine initialized with domain: PhysicsDomain.KINEMATICS
2025-08-16 18:09:40,946 - root - ERROR - Application error: 'BlenderLikeUI' object has no attribute 'viewport_plot'
2025-08-16 18:09:41,352 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-08-16 18:11:42,332 - root - WARNING - Failed to initialize LLM parser: OpenAI package not installed. Run: pip install openai
2025-08-16 18:11:42,332 - root - INFO - Initialized PhysicsDomain.KINEMATICS physics engine
2025-08-16 18:11:42,332 - root - INFO - SimulationEngine initialized with domain: PhysicsDomain.KINEMATICS
2025-08-16 18:11:42,788 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-08-16 18:11:45,991 - root - ERROR - Application error: Cannot call click outside of a gradio.Blocks context.
2025-08-16 18:12:52,568 - root - WARNING - Failed to initialize LLM parser: OpenAI package not installed. Run: pip install openai
2025-08-16 18:12:52,568 - root - INFO - Initialized PhysicsDomain.KINEMATICS physics engine
2025-08-16 18:12:52,569 - root - INFO - SimulationEngine initialized with domain: PhysicsDomain.KINEMATICS
2025-08-16 18:12:53,013 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-08-16 18:12:53,317 - root - ERROR - Application error: Cannot call click outside of a gradio.Blocks context.
