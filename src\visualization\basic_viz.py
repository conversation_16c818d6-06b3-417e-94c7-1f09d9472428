"""
Basic visualization utilities for 3D physics simulations.
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from mpl_toolkits.mplot3d import Axes3D
import plotly.graph_objects as go
import plotly.express as px
from typing import Op<PERSON>, Tuple, List


def plot_2d_slices(temperature_field: np.ndarray, 
                  slice_indices: Optional[List[int]] = None,
                  title: str = "Temperature Field",
                  save_path: Optional[str] = None):
    """
    Plot 2D slices of a 3D temperature field.
    
    Args:
        temperature_field: 3D array of shape (nx, ny, nz)
        slice_indices: list of z-indices to plot (default: middle slices)
        title: plot title
        save_path: path to save the plot
    """
    nx, ny, nz = temperature_field.shape
    
    if slice_indices is None:
        slice_indices = [nz//4, nz//2, 3*nz//4]
    
    n_slices = len(slice_indices)
    fig, axes = plt.subplots(1, n_slices, figsize=(4*n_slices, 4))
    
    if n_slices == 1:
        axes = [axes]
    
    vmin, vmax = temperature_field.min(), temperature_field.max()
    
    for i, z_idx in enumerate(slice_indices):
        im = axes[i].imshow(temperature_field[:, :, z_idx], 
                           cmap='hot', vmin=vmin, vmax=vmax, origin='lower')
        axes[i].set_title(f'Z = {z_idx}')
        axes[i].set_xlabel('Y')
        axes[i].set_ylabel('X')
        plt.colorbar(im, ax=axes[i])
    
    plt.suptitle(title)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
    
    plt.show()


def plot_3d_isosurface(temperature_field: np.ndarray,
                      threshold: float = None,
                      title: str = "3D Temperature Field",
                      save_path: Optional[str] = None):
    """
    Plot 3D isosurface using plotly.
    
    Args:
        temperature_field: 3D array of shape (nx, ny, nz)
        threshold: temperature threshold for isosurface (default: 50% of max)
        title: plot title
        save_path: path to save the plot
    """
    if threshold is None:
        threshold = 0.5 * temperature_field.max()
    
    # Create coordinate grids
    nx, ny, nz = temperature_field.shape
    x, y, z = np.meshgrid(np.arange(nx), np.arange(ny), np.arange(nz), indexing='ij')
    
    # Create isosurface
    fig = go.Figure(data=go.Isosurface(
        x=x.flatten(),
        y=y.flatten(),
        z=z.flatten(),
        value=temperature_field.flatten(),
        isomin=threshold,
        isomax=temperature_field.max(),
        surface_count=3,
        colorscale='Hot',
        caps=dict(x_show=False, y_show=False, z_show=False)
    ))
    
    fig.update_layout(
        title=title,
        scene=dict(
            xaxis_title='X',
            yaxis_title='Y',
            zaxis_title='Z',
            aspectmode='cube'
        ),
        width=800,
        height=600
    )
    
    if save_path:
        fig.write_html(save_path)
    
    fig.show()


def plot_volume_render(temperature_field: np.ndarray,
                      title: str = "3D Volume Rendering",
                      opacity: float = 0.1,
                      save_path: Optional[str] = None):
    """
    Plot 3D volume rendering using plotly.
    
    Args:
        temperature_field: 3D array of shape (nx, ny, nz)
        title: plot title
        opacity: volume opacity
        save_path: path to save the plot
    """
    # Create coordinate grids
    nx, ny, nz = temperature_field.shape
    x, y, z = np.meshgrid(np.arange(nx), np.arange(ny), np.arange(nz), indexing='ij')
    
    # Create volume plot
    fig = go.Figure(data=go.Volume(
        x=x.flatten(),
        y=y.flatten(),
        z=z.flatten(),
        value=temperature_field.flatten(),
        isomin=0.1 * temperature_field.max(),
        isomax=temperature_field.max(),
        opacity=opacity,
        surface_count=15,
        colorscale='Hot'
    ))
    
    fig.update_layout(
        title=title,
        scene=dict(
            xaxis_title='X',
            yaxis_title='Y',
            zaxis_title='Z',
            aspectmode='cube'
        ),
        width=800,
        height=600
    )
    
    if save_path:
        fig.write_html(save_path)
    
    fig.show()


def animate_simulation(simulation_history: np.ndarray,
                      slice_z: Optional[int] = None,
                      interval: int = 100,
                      title: str = "Physics Simulation",
                      save_path: Optional[str] = None):
    """
    Create animation of simulation evolution.
    
    Args:
        simulation_history: array of shape (n_steps, nx, ny, nz)
        slice_z: z-index for 2D slice animation (default: middle)
        interval: animation interval in milliseconds
        title: animation title
        save_path: path to save animation (as gif)
    """
    n_steps, nx, ny, nz = simulation_history.shape
    
    if slice_z is None:
        slice_z = nz // 2
    
    # Setup figure
    fig, ax = plt.subplots(figsize=(8, 6))
    
    vmin, vmax = simulation_history.min(), simulation_history.max()
    
    # Initial frame
    im = ax.imshow(simulation_history[0, :, :, slice_z], 
                   cmap='hot', vmin=vmin, vmax=vmax, origin='lower')
    ax.set_xlabel('Y')
    ax.set_ylabel('X')
    ax.set_title(f'{title} (Z = {slice_z})')
    
    # Add colorbar
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('Temperature')
    
    # Add time text
    time_text = ax.text(0.02, 0.95, '', transform=ax.transAxes, 
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    def animate(frame):
        im.set_array(simulation_history[frame, :, :, slice_z])
        time_text.set_text(f'Step: {frame}/{n_steps-1}')
        return [im, time_text]
    
    # Create animation
    anim = FuncAnimation(fig, animate, frames=n_steps, interval=interval, blit=True)
    
    if save_path:
        anim.save(save_path, writer='pillow', fps=10)
        print(f"Animation saved to {save_path}")
    
    plt.tight_layout()
    plt.show()
    
    return anim


def compare_predictions(ground_truth: np.ndarray,
                       predictions: np.ndarray,
                       time_step: int = -1,
                       slice_z: Optional[int] = None,
                       title: str = "Prediction Comparison"):
    """
    Compare ground truth vs predictions.
    
    Args:
        ground_truth: true temperature field
        predictions: predicted temperature field
        time_step: time step to visualize (-1 for last)
        slice_z: z-index for slice (default: middle)
        title: plot title
    """
    if len(ground_truth.shape) == 4:  # Time series
        gt = ground_truth[time_step]
        pred = predictions[time_step]
    else:  # Single time step
        gt = ground_truth
        pred = predictions
    
    if slice_z is None:
        slice_z = gt.shape[-1] // 2
    
    # Extract slices
    gt_slice = gt[:, :, slice_z]
    pred_slice = pred[:, :, slice_z]
    error_slice = np.abs(gt_slice - pred_slice)
    
    # Plot comparison
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    vmin = min(gt_slice.min(), pred_slice.min())
    vmax = max(gt_slice.max(), pred_slice.max())
    
    # Ground truth
    im1 = axes[0].imshow(gt_slice, cmap='hot', vmin=vmin, vmax=vmax, origin='lower')
    axes[0].set_title('Ground Truth')
    axes[0].set_xlabel('Y')
    axes[0].set_ylabel('X')
    plt.colorbar(im1, ax=axes[0])
    
    # Prediction
    im2 = axes[1].imshow(pred_slice, cmap='hot', vmin=vmin, vmax=vmax, origin='lower')
    axes[1].set_title('Prediction')
    axes[1].set_xlabel('Y')
    axes[1].set_ylabel('X')
    plt.colorbar(im2, ax=axes[1])
    
    # Error
    im3 = axes[2].imshow(error_slice, cmap='Reds', origin='lower')
    axes[2].set_title('Absolute Error')
    axes[2].set_xlabel('Y')
    axes[2].set_ylabel('X')
    plt.colorbar(im3, ax=axes[2])
    
    plt.suptitle(f'{title} (Z = {slice_z})')
    plt.tight_layout()
    plt.show()
    
    # Print statistics
    mse = np.mean((gt - pred)**2)
    mae = np.mean(np.abs(gt - pred))
    print(f"MSE: {mse:.6f}")
    print(f"MAE: {mae:.6f}")


if __name__ == "__main__":
    # Test visualization with dummy data
    from ..physics.heat_diffusion import create_random_initial_condition
    
    # Create test data
    temp_field = create_random_initial_condition((32, 32, 32), n_sources=2)
    
    # Test 2D slices
    plot_2d_slices(temp_field, title="Test Temperature Field")
    
    print("Visualization test completed!")
