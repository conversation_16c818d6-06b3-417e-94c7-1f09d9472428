#!/usr/bin/env python3
"""
Simple Web Demo for NeoPhysics Phase 2

A working web interface that demonstrates the core functionality
without the complex Blender-like UI that has event handler issues.
"""

import os
import sys
import gradio as gr
import plotly.graph_objects as go
import numpy as np

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.simulation_engine import SimulationEngine
from src.physics.base import PhysicsDomain


class SimpleWebUI:
    """Simple web interface for NeoPhysics."""
    
    def __init__(self):
        """Initialize the simple web UI."""
        self.engine = SimulationEngine(
            default_domain=PhysicsDomain.KINEMATICS,
            parser_type="keyword"  # Use keyword parser to avoid LLM dependencies
        )
        
        self.command_history = []
        self.execution_log = []
    
    def execute_command(self, command_text):
        """Execute a natural language command."""
        if not command_text.strip():
            return self._get_current_state()
        
        try:
            # Execute command
            results = self.engine.execute_command(command_text)
            
            # Update history
            self.command_history.append(f"🔥 {command_text}")
            
            # Process results
            for result in results:
                if result.get('success'):
                    self.execution_log.append(f"✅ {result.get('message', 'Success')}")
                else:
                    self.execution_log.append(f"❌ {result.get('error', 'Failed')}")
            
            return self._get_current_state()
            
        except Exception as e:
            self.execution_log.append(f"❌ Error: {str(e)}")
            return self._get_current_state()
    
    def update_gravity(self, gravity_value):
        """Update gravity parameter."""
        try:
            command = f"set gravity to {gravity_value}"
            self.engine.execute_command(command)
            self.execution_log.append(f"🔧 Gravity set to {gravity_value} m/s²")
            return self._get_current_state()
        except Exception as e:
            self.execution_log.append(f"❌ Gravity update failed: {e}")
            return self._get_current_state()
    
    def reset_simulation(self):
        """Reset the simulation."""
        try:
            self.engine.reset_simulation()
            self.command_history.clear()
            self.execution_log.clear()
            self.execution_log.append("🔄 Simulation reset")
            return self._get_current_state()
        except Exception as e:
            self.execution_log.append(f"❌ Reset failed: {e}")
            return self._get_current_state()
    
    def _get_current_state(self):
        """Get current UI state."""
        # Create 3D plot
        plot = self._create_3d_plot()
        
        # Get analytics
        analytics = self.engine.get_analytics()
        
        # Format history and log
        history_text = "\n".join(self.command_history[-10:])
        log_text = "\n".join(self.execution_log[-15:])
        
        # Create object table
        object_data = []
        for obj_name, scene_obj in self.engine.scene.objects.items():
            if scene_obj.physics_object:
                pos = scene_obj.transform.position
                vel = getattr(scene_obj.physics_object, 'velocity', None)
                mass = getattr(scene_obj.physics_object, 'mass', 'N/A')
                
                object_data.append([
                    obj_name[:15],  # Truncate long names
                    getattr(scene_obj.physics_object, 'shape', 'unknown'),
                    f"{mass:.1f}" if isinstance(mass, (int, float)) else str(mass),
                    f"({pos.x:.1f}, {pos.y:.1f}, {pos.z:.1f})",
                    f"({vel.x:.1f}, {vel.y:.1f}, {vel.z:.1f})" if vel else "N/A"
                ])
        
        return plot, analytics, object_data, history_text, log_text
    
    def _create_3d_plot(self):
        """Create 3D plot of the current scene."""
        fig = go.Figure()
        
        # Add coordinate axes
        fig.add_trace(go.Scatter3d(
            x=[0, 5], y=[0, 0], z=[0, 0],
            mode='lines',
            line=dict(color='red', width=8),
            name='X-axis'
        ))
        
        fig.add_trace(go.Scatter3d(
            x=[0, 0], y=[0, 5], z=[0, 0],
            mode='lines',
            line=dict(color='green', width=8),
            name='Y-axis'
        ))
        
        fig.add_trace(go.Scatter3d(
            x=[0, 0], y=[0, 0], z=[0, 5],
            mode='lines',
            line=dict(color='blue', width=8),
            name='Z-axis'
        ))
        
        # Add physics objects
        for obj_name, scene_obj in self.engine.scene.objects.items():
            if scene_obj.physics_object:
                self._add_object_to_plot(fig, scene_obj)
        
        # Configure layout
        fig.update_layout(
            scene=dict(
                xaxis_title='X (m)',
                yaxis_title='Y (m)',
                zaxis_title='Z (m)',
                aspectmode='cube',
                camera=dict(
                    eye=dict(x=1.5, y=1.5, z=1.5),
                    center=dict(x=0, y=0, z=0),
                    up=dict(x=0, y=1, z=0)
                )
            ),
            title="🔥 NeoPhysics 3D Scene",
            showlegend=True,
            margin=dict(l=0, r=0, t=50, b=0),
            height=500
        )
        
        return fig
    
    def _add_object_to_plot(self, fig, scene_obj):
        """Add object to 3D plot."""
        pos = scene_obj.transform.position
        physics_obj = scene_obj.physics_object
        
        if hasattr(physics_obj, 'shape'):
            if physics_obj.shape == "sphere":
                # Add sphere
                radius = physics_obj.size.x
                fig.add_trace(go.Scatter3d(
                    x=[pos.x], y=[pos.y], z=[pos.z],
                    mode='markers',
                    marker=dict(
                        size=max(10, radius * 20),
                        color='red',
                        opacity=0.8
                    ),
                    name=f"Ball (m={physics_obj.mass:.1f}kg)"
                ))
            
            elif physics_obj.shape == "cube":
                # Add cube
                fig.add_trace(go.Scatter3d(
                    x=[pos.x], y=[pos.y], z=[pos.z],
                    mode='markers',
                    marker=dict(
                        size=15,
                        color='green',
                        symbol='square',
                        opacity=0.8
                    ),
                    name=f"Cube (m={physics_obj.mass:.1f}kg)"
                ))
        
        # Add terrain as surface
        if hasattr(physics_obj, 'height_function'):
            self._add_terrain_to_plot(fig, physics_obj)
    
    def _add_terrain_to_plot(self, fig, terrain_obj):
        """Add terrain to plot."""
        x = np.linspace(-10, 10, 20)
        z = np.linspace(-10, 10, 20)
        X, Z = np.meshgrid(x, z)
        Y = np.zeros_like(X)
        
        # Apply height function
        for i in range(len(x)):
            for j in range(len(z)):
                Y[j, i] = terrain_obj.get_height_at(X[j, i], Z[j, i])
        
        fig.add_trace(go.Surface(
            x=X, y=Y, z=Z,
            colorscale='Earth',
            opacity=0.7,
            name="Terrain",
            showscale=False
        ))
    
    def create_interface(self):
        """Create the Gradio interface."""
        with gr.Blocks(title="NeoPhysics: Simple Web Demo", theme=gr.themes.Soft()) as app:
            
            gr.Markdown("# 🔥 NeoPhysics: 3D Generative Physics Sandbox")
            gr.Markdown("*Simple web interface demonstrating natural language physics control*")
            
            with gr.Row():
                # Left column - Controls
                with gr.Column(scale=1):
                    gr.Markdown("### 🎮 Controls")
                    
                    command_input = gr.Textbox(
                        label="Natural Language Command",
                        placeholder="e.g., 'create a ball with mass 5kg' or 'make a hill with height 10 meters'",
                        lines=3
                    )
                    
                    with gr.Row():
                        execute_btn = gr.Button("🚀 Execute", variant="primary")
                        reset_btn = gr.Button("🔄 Reset", variant="secondary")
                    
                    gr.Markdown("### ⚙️ Physics Parameters")
                    gravity_slider = gr.Slider(
                        minimum=0, maximum=20, value=9.81, step=0.1,
                        label="Gravity (m/s²)"
                    )
                    
                    gr.Markdown("### 📝 Command History")
                    history_display = gr.Textbox(
                        label="Recent Commands",
                        lines=5,
                        interactive=False
                    )
                    
                    gr.Markdown("### 🔍 Execution Log")
                    log_display = gr.Textbox(
                        label="Results",
                        lines=5,
                        interactive=False
                    )
                
                # Right column - Visualization and Data
                with gr.Column(scale=2):
                    gr.Markdown("### 🌐 3D Scene")
                    plot_display = gr.Plot(label="3D Visualization")
                    
                    gr.Markdown("### 📊 Analytics")
                    analytics_display = gr.JSON(label="Simulation Data")
                    
                    gr.Markdown("### 🎯 Scene Objects")
                    object_table = gr.Dataframe(
                        headers=["Name", "Type", "Mass", "Position", "Velocity"],
                        datatype=["str", "str", "str", "str", "str"],
                        label="Objects"
                    )
            
            # Set up event handlers
            execute_btn.click(
                self.execute_command,
                inputs=[command_input],
                outputs=[plot_display, analytics_display, object_table, history_display, log_display]
            )
            
            reset_btn.click(
                self.reset_simulation,
                inputs=[],
                outputs=[plot_display, analytics_display, object_table, history_display, log_display]
            )
            
            gravity_slider.change(
                self.update_gravity,
                inputs=[gravity_slider],
                outputs=[plot_display, analytics_display, object_table, history_display, log_display]
            )
            
            # Initialize with current state
            app.load(
                lambda: self._get_current_state(),
                outputs=[plot_display, analytics_display, object_table, history_display, log_display]
            )
        
        return app


def main():
    """Main function."""
    print("🌐 Launching NeoPhysics Simple Web Interface...")
    print("=" * 50)
    
    # Create UI
    ui = SimpleWebUI()
    app = ui.create_interface()
    
    print("🚀 Starting web server...")
    print("📱 Interface will open in your browser")
    print("🔗 URL: http://localhost:7860")
    print("⏹️  Press Ctrl+C to stop")
    
    # Launch
    app.launch(
        share=False,
        server_name="0.0.0.0",
        server_port=7860,
        show_error=True
    )


if __name__ == "__main__":
    main()
