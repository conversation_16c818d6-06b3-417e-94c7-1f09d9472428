"""
Command Executor for Physics Commands.

This module executes structured physics commands by interfacing with
the appropriate physics engines and scene management systems.
"""

from typing import Dict, Any, Optional
import logging

from ..language.parser import PhysicsCommand, CommandType
from ..physics.base import PhysicsDomain, Vector3D
from ..physics.kinematics import <PERSON><PERSON><PERSON><PERSON><PERSON>, Terrain, Material


class CommandExecutor:
    """Executes structured physics commands."""
    
    def __init__(self, simulation_engine):
        """
        Initialize command executor.
        
        Args:
            simulation_engine: reference to main simulation engine
        """
        self.engine = simulation_engine
        
        # Command handlers
        self.handlers = {
            CommandType.CREATE_OBJECT: self._handle_create_object,
            CommandType.MODIFY_OBJECT: self._handle_modify_object,
            CommandType.SET_PARAMETER: self._handle_set_parameter,
            CommandType.RUN_SIMULATION: self._handle_run_simulation,
            CommandType.ANALYZE_DATA: self._handle_analyze_data,
            CommandType.RESET_SIMULATION: self._handle_reset_simulation,
        }
    
    def execute(self, command: PhysicsCommand) -> Dict[str, Any]:
        """
        Execute a physics command.
        
        Args:
            command: structured physics command
            
        Returns:
            Execution result dictionary
        """
        try:
            # Check if we need to switch physics domains
            if command.domain != self.engine.default_domain:
                success = self.engine.switch_physics_domain(command.domain)
                if not success:
                    return {
                        'success': False,
                        'error': f"Failed to switch to {command.domain} domain"
                    }
            
            # Get appropriate handler
            handler = self.handlers.get(command.command_type)
            if not handler:
                return {
                    'success': False,
                    'error': f"No handler for command type: {command.command_type}"
                }
            
            # Execute command
            result = handler(command)
            
            # Add metadata
            result.update({
                'command': command.to_dict(),
                'execution_time': self.engine.simulation_time
            })
            
            return result
            
        except Exception as e:
            logging.error(f"Command execution failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'command': command.to_dict()
            }
    
    def _handle_create_object(self, command: PhysicsCommand) -> Dict[str, Any]:
        """Handle object creation commands."""
        action = command.action
        params = command.parameters
        
        if command.domain == PhysicsDomain.KINEMATICS:
            return self._create_kinematics_object(action, params)
        else:
            return {
                'success': False,
                'error': f"Object creation not implemented for domain: {command.domain}"
            }
    
    def _create_kinematics_object(self, action: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create kinematics objects."""
        engine = self.engine.current_engine
        
        if action == "create_ball":
            return self._create_ball(engine, params)
        elif action == "create_hill":
            return self._create_hill(engine, params)
        elif action == "create_cube":
            return self._create_cube(engine, params)
        else:
            return {
                'success': False,
                'error': f"Unknown kinematics object type: {action}"
            }
    
    def _create_ball(self, engine, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create a ball (sphere) object."""
        try:
            # Extract parameters with defaults
            mass = params.get('mass', 1.0)
            radius = params.get('radius', 0.5)
            position = params.get('position', [0, 5, 0])
            
            # Convert position to Vector3D
            if isinstance(position, (list, tuple)):
                position = Vector3D(position[0], position[1], position[2])
            elif hasattr(position, '__len__') and len(position) >= 3:  # numpy array
                position = Vector3D(float(position[0]), float(position[1]), float(position[2]))
            
            # Create material
            material_params = params.get('material', {})
            material = Material(
                friction=material_params.get('friction', 0.4),
                restitution=material_params.get('restitution', 0.6),
                density=material_params.get('density', 1.0)
            )
            
            # Create ball using engine method
            ball = engine.create_ball(mass, radius, position)
            ball.material = material
            
            return {
                'success': True,
                'object_id': ball.id,
                'object_type': 'ball',
                'message': f"Created ball with mass {mass}kg, radius {radius}m at {position.to_array()}"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Failed to create ball: {e}"
            }
    
    def _create_hill(self, engine, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create a sloped hill terrain."""
        try:
            height = params.get('height', 5.0)
            slope_angle = params.get('slope_angle', 30.0)
            
            # Create hill using engine method
            hill = engine.create_sloped_hill(height, slope_angle)
            
            return {
                'success': True,
                'object_id': hill.id,
                'object_type': 'hill',
                'message': f"Created hill with height {height}m and slope {slope_angle}°"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Failed to create hill: {e}"
            }
    
    def _create_cube(self, engine, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create a cube object."""
        try:
            mass = params.get('mass', 1.0)
            size = params.get('size', [1, 1, 1])
            position = params.get('position', [0, 5, 0])
            
            # Convert to Vector3D
            if isinstance(size, list):
                size = Vector3D(size[0], size[1], size[2])
            if isinstance(position, list):
                position = Vector3D(position[0], position[1], position[2])
            
            # Create material
            material_params = params.get('material', {})
            material = Material(
                friction=material_params.get('friction', 0.5),
                restitution=material_params.get('restitution', 0.3),
                density=material_params.get('density', 1.0)
            )
            
            # Create cube
            cube = RigidBody(
                name="cube",
                mass=mass,
                shape="cube",
                size=size,
                material=material
            )
            cube.transform.position = position
            
            engine.world.add_object(cube)
            
            return {
                'success': True,
                'object_id': cube.id,
                'object_type': 'cube',
                'message': f"Created cube with mass {mass}kg, size {size.to_array()} at {position.to_array()}"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Failed to create cube: {e}"
            }
    
    def _handle_modify_object(self, command: PhysicsCommand) -> Dict[str, Any]:
        """Handle object modification commands."""
        # This would implement object property changes
        return {
            'success': False,
            'error': "Object modification not yet implemented"
        }
    
    def _handle_set_parameter(self, command: PhysicsCommand) -> Dict[str, Any]:
        """Handle global parameter setting commands."""
        action = command.action
        params = command.parameters
        engine = self.engine.current_engine
        
        if not engine:
            return {
                'success': False,
                'error': "No physics engine available"
            }
        
        if action == "set_gravity":
            gravity_value = params.get('gravity', 9.81)
            gravity_vector = Vector3D(0, -gravity_value, 0)
            engine.world.set_global_param('gravity', gravity_vector)
            
            return {
                'success': True,
                'message': f"Set gravity to {gravity_value} m/s²"
            }
        
        elif action == "set_air_resistance":
            resistance = params.get('air_resistance', 0.01)
            engine.world.set_global_param('air_resistance', resistance)
            
            return {
                'success': True,
                'message': f"Set air resistance to {resistance}"
            }
        
        else:
            return {
                'success': False,
                'error': f"Unknown parameter setting: {action}"
            }
    
    def _handle_run_simulation(self, command: PhysicsCommand) -> Dict[str, Any]:
        """Handle simulation execution commands."""
        params = command.parameters
        duration = params.get('duration', 10.0)
        
        try:
            # Start simulation in a separate thread or async manner
            # For now, we'll just prepare for simulation
            self.engine.start_simulation(duration)
            
            return {
                'success': True,
                'message': f"Started simulation for {duration} seconds"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Failed to start simulation: {e}"
            }
    
    def _handle_analyze_data(self, command: PhysicsCommand) -> Dict[str, Any]:
        """Handle data analysis commands."""
        try:
            analytics = self.engine.get_analytics()
            
            # Extract specific analytics based on command parameters
            params = command.parameters
            analysis_type = params.get('type', 'summary')
            
            if analysis_type == 'summary':
                return {
                    'success': True,
                    'data': analytics,
                    'message': "Retrieved simulation analytics"
                }
            
            elif analysis_type == 'object_data':
                object_name = params.get('object_name')
                if object_name and object_name in analytics.get('analytics', {}):
                    object_data = analytics['analytics'][object_name]
                    return {
                        'success': True,
                        'data': object_data,
                        'message': f"Retrieved data for object: {object_name}"
                    }
                else:
                    return {
                        'success': False,
                        'error': f"Object not found: {object_name}"
                    }
            
            else:
                return {
                    'success': False,
                    'error': f"Unknown analysis type: {analysis_type}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Failed to analyze data: {e}"
            }
    
    def _handle_reset_simulation(self, command: PhysicsCommand) -> Dict[str, Any]:
        """Handle simulation reset commands."""
        try:
            self.engine.reset_simulation()
            
            return {
                'success': True,
                'message': "Simulation reset successfully"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Failed to reset simulation: {e}"
            }
