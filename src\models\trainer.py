"""
Training utilities for the 3D physics prediction model.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
import numpy as np
import os
from typing import Tuple, Optional
from tqdm import tqdm
import matplotlib.pyplot as plt

from .unet3d import PhysicsPredictor
from ..utils.dataset import PhysicsDataset


class PhysicsDatasetTorch(Dataset):
    """PyTorch Dataset wrapper for physics data."""
    
    def __init__(self, inputs: np.ndarray, targets: np.ndarray):
        """
        Initialize dataset.
        
        Args:
            inputs: input states of shape (n_samples, nx, ny, nz)
            targets: target states of shape (n_samples, nx, ny, nz)
        """
        self.inputs = torch.from_numpy(inputs).float()
        self.targets = torch.from_numpy(targets).float()
    
    def __len__(self):
        return len(self.inputs)
    
    def __getitem__(self, idx):
        return self.inputs[idx], self.targets[idx]


class PhysicsTrainer:
    """Trainer for physics prediction models."""
    
    def __init__(self, 
                 model: PhysicsPredictor,
                 device: torch.device = None):
        """
        Initialize trainer.
        
        Args:
            model: physics prediction model
            device: device to train on
        """
        self.model = model
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        
        # Training history
        self.train_losses = []
        self.val_losses = []
    
    def train_epoch(self, 
                   dataloader: DataLoader, 
                   optimizer: optim.Optimizer, 
                   criterion: nn.Module) -> float:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        
        for batch_inputs, batch_targets in tqdm(dataloader, desc="Training"):
            batch_inputs = batch_inputs.to(self.device)
            batch_targets = batch_targets.to(self.device)
            
            optimizer.zero_grad()
            
            # Forward pass
            predictions = self.model(batch_inputs)
            loss = criterion(predictions, batch_targets)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        return total_loss / len(dataloader)
    
    def validate_epoch(self, 
                      dataloader: DataLoader, 
                      criterion: nn.Module) -> float:
        """Validate for one epoch."""
        self.model.eval()
        total_loss = 0.0
        
        with torch.no_grad():
            for batch_inputs, batch_targets in dataloader:
                batch_inputs = batch_inputs.to(self.device)
                batch_targets = batch_targets.to(self.device)
                
                predictions = self.model(batch_inputs)
                loss = criterion(predictions, batch_targets)
                
                total_loss += loss.item()
        
        return total_loss / len(dataloader)
    
    def train(self, 
             train_dataset: PhysicsDatasetTorch,
             val_dataset: PhysicsDatasetTorch,
             n_epochs: int = 100,
             batch_size: int = 8,
             learning_rate: float = 1e-3,
             save_path: Optional[str] = None) -> dict:
        """
        Train the model.
        
        Args:
            train_dataset: training dataset
            val_dataset: validation dataset
            n_epochs: number of training epochs
            batch_size: batch size
            learning_rate: learning rate
            save_path: path to save the best model
            
        Returns:
            training history dictionary
        """
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        # Setup training
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        best_val_loss = float('inf')
        
        print(f"Training on {self.device}")
        print(f"Train samples: {len(train_dataset)}")
        print(f"Val samples: {len(val_dataset)}")
        
        for epoch in range(n_epochs):
            # Train
            train_loss = self.train_epoch(train_loader, optimizer, criterion)
            
            # Validate
            val_loss = self.validate_epoch(val_loader, criterion)
            
            # Update learning rate
            scheduler.step(val_loss)
            
            # Save history
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            
            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                if save_path:
                    self.save_model(save_path)
            
            # Print progress
            print(f"Epoch {epoch+1}/{n_epochs}: "
                  f"Train Loss: {train_loss:.6f}, "
                  f"Val Loss: {val_loss:.6f}, "
                  f"LR: {optimizer.param_groups[0]['lr']:.2e}")
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': best_val_loss
        }
    
    def save_model(self, save_path: str):
        """Save model checkpoint."""
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'grid_size': self.model.grid_size,
            'use_residual': self.model.use_residual
        }
        
        torch.save(checkpoint, save_path)
        print(f"Model saved to {save_path}")
    
    def load_model(self, load_path: str):
        """Load model checkpoint."""
        checkpoint = torch.load(load_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.train_losses = checkpoint.get('train_losses', [])
        self.val_losses = checkpoint.get('val_losses', [])
        
        print(f"Model loaded from {load_path}")
    
    def plot_training_history(self, save_path: Optional[str] = None):
        """Plot training history."""
        plt.figure(figsize=(10, 6))
        plt.plot(self.train_losses, label='Training Loss')
        plt.plot(self.val_losses, label='Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training History')
        plt.legend()
        plt.grid(True)
        
        if save_path:
            plt.savefig(save_path)
        plt.show()


def prepare_datasets(data_path: str, 
                    val_split: float = 0.2) -> Tuple[PhysicsDatasetTorch, PhysicsDatasetTorch]:
    """
    Load and split dataset into train/validation.
    
    Args:
        data_path: path to the dataset file
        val_split: fraction of data to use for validation
        
    Returns:
        (train_dataset, val_dataset)
    """
    inputs, targets, metadata = PhysicsDataset.load_dataset(data_path)
    
    # Create PyTorch dataset
    full_dataset = PhysicsDatasetTorch(inputs, targets)
    
    # Split into train/validation
    n_val = int(len(full_dataset) * val_split)
    n_train = len(full_dataset) - n_val
    
    train_dataset, val_dataset = random_split(full_dataset, [n_train, n_val])
    
    return train_dataset, val_dataset


if __name__ == "__main__":
    # Example training script
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Create model
    model = PhysicsPredictor(grid_size=(32, 32, 32))
    trainer = PhysicsTrainer(model, device)
    
    print(f"Model has {sum(p.numel() for p in model.parameters() if p.requires_grad):,} parameters")
    print("Ready to train! Load a dataset and call trainer.train()")
