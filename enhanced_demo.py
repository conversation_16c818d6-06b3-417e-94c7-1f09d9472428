#!/usr/bin/env python3
"""
Enhanced NeoPhysics Demo - Full-Screen 3D Physics Sandbox

This demo showcases the refined features:
1. Full-screen 3D environment with overlay UI
2. Enhanced neural networks with FNO and attention
3. Origin-centered object placement
4. Advanced visualization features
5. Comprehensive physics domains
"""

import os
import sys
import torch
import numpy as np
import argparse
import time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.visualization.fullscreen_ui import FullScreenPhysicsUI
from src.visualization.advanced_viz import AdvancedPhysicsVisualizer
from src.models.enhanced_networks import EnhancedPhysicsNet, PhysicsAwareLoss, count_parameters
from src.models.enhanced_trainer import EnhancedPhysicsTrainer
from src.physics.heat_diffusion import HeatDiffusion3D, create_origin_centered_initial_condition
from src.physics.base import ensure_origin_based_position, Vector3D
from src.core.simulation_engine import SimulationEngine
from src.physics.base import PhysicsDomain


def demo_enhanced_neural_network():
    """Demonstrate the enhanced neural network architecture."""
    print("🧠 Enhanced Neural Network Demo")
    print("=" * 50)
    
    # Create enhanced network (smaller for demo)
    model = EnhancedPhysicsNet(
        in_channels=1,
        out_channels=1,
        hidden_channels=32,   # Smaller for demo
        num_layers=4,         # Fewer layers for demo
        modes=(8, 8, 8),      # Lower resolution for demo
        use_residual=True
    )
    
    print(f"Model parameters: {count_parameters(model):,}")
    
    # Test with sample data (smaller for demo)
    batch_size = 2
    grid_size = 32
    sample_input = torch.randn(batch_size, 1, grid_size, grid_size, grid_size)
    
    print(f"Input shape: {sample_input.shape}")
    
    # Forward pass
    with torch.no_grad():
        output = model(sample_input)
        print(f"Output shape: {output.shape}")
    
    # Test physics-aware loss
    loss_fn = PhysicsAwareLoss(
        mse_weight=1.0,
        conservation_weight=0.1,
        boundary_weight=0.1,
        divergence_weight=0.1
    )
    
    target = torch.randn_like(output)
    loss = loss_fn(output, target)
    print(f"Physics-aware loss: {loss.item():.6f}")
    
    print("✅ Enhanced neural network demo completed\n")


def demo_origin_centered_physics():
    """Demonstrate origin-centered object placement."""
    print("🎯 Origin-Centered Physics Demo")
    print("=" * 50)
    
    # Test origin-based positioning
    positions = [
        None,  # Should default to origin
        [1, 2, 3],  # Explicit position
        Vector3D(0.5, -0.5, 1.0),  # Vector3D
        np.array([2, 0, -1])  # Numpy array
    ]
    
    for i, pos in enumerate(positions):
        result = ensure_origin_based_position(pos)
        print(f"Position {i}: {pos} -> {result}")
    
    # Create heat diffusion with origin-centered sources
    print("\nCreating origin-centered heat sources...")
    initial_condition = create_origin_centered_initial_condition(
        grid_size=(64, 64, 64),
        n_sources=5,
        temp_range=(50.0, 150.0),
        max_distance_from_origin=10
    )
    
    print(f"Heat field shape: {initial_condition.shape}")
    print(f"Temperature range: {initial_condition.min():.2f} - {initial_condition.max():.2f}")
    
    # Check that sources are near center
    center = np.array(initial_condition.shape) // 2
    hot_spots = np.where(initial_condition > 40)
    if len(hot_spots[0]) > 0:
        distances = np.sqrt(
            (hot_spots[0] - center[0])**2 + 
            (hot_spots[1] - center[1])**2 + 
            (hot_spots[2] - center[2])**2
        )
        print(f"Average distance from center: {distances.mean():.2f} voxels")
    
    print("✅ Origin-centered physics demo completed\n")


def demo_advanced_visualization():
    """Demonstrate advanced visualization features."""
    print("🎨 Advanced Visualization Demo")
    print("=" * 50)
    
    # Create sample 3D field data centered at origin
    grid_size = 32
    x = np.linspace(-5, 5, grid_size)
    y = np.linspace(-5, 5, grid_size)
    z = np.linspace(-5, 5, grid_size)
    X, Y, Z = np.meshgrid(x, y, z, indexing='ij')
    
    # Create a temperature field with hot spot at origin
    field_data = 20 + 50 * np.exp(-(X**2 + Y**2 + Z**2) / 4)
    
    # Initialize visualizer
    viz = AdvancedPhysicsVisualizer()
    
    # Create volume rendering
    print("Creating volume rendering...")
    vol_fig = viz.create_volume_rendering(
        field_data, 
        field_type='temperature',
        opacity=0.1,
        show_isosurfaces=True
    )
    
    # Create vector field (simple radial field from origin)
    positions = []
    vectors = []
    for i in range(0, grid_size, 4):
        for j in range(0, grid_size, 4):
            for k in range(0, grid_size, 4):
                pos = np.array([x[i], y[j], z[k]])
                vec = -pos * 0.1  # Radial inward field
                positions.append(pos)
                vectors.append(vec)
    
    positions = np.array(positions)
    vectors = np.array(vectors)
    
    print("Creating vector field visualization...")
    vec_fig = viz.create_vector_field_visualization(
        positions, vectors, 
        field_type='velocity',
        arrow_scale=2.0
    )
    
    # Create heat map slices
    print("Creating heat map slices...")
    slice_fig = viz.create_heat_map_slices(
        field_data,
        field_type='temperature'
    )
    
    print("✅ Advanced visualization demo completed\n")
    
    return vol_fig, vec_fig, slice_fig


def demo_enhanced_training():
    """Demonstrate enhanced training pipeline."""
    print("🏋️ Enhanced Training Demo")
    print("=" * 50)
    
    # Create model and trainer
    model = EnhancedPhysicsNet(
        in_channels=1,
        out_channels=1,
        hidden_channels=64,
        num_layers=4,
        modes=(8, 8, 8)
    )
    
    trainer = EnhancedPhysicsTrainer(
        model=model,
        learning_rate=1e-3,
        use_physics_loss=True,
        multi_step_training=True,
        max_rollout_steps=3
    )
    
    print(f"Trainer initialized with {count_parameters(model):,} parameters")
    print(f"Using device: {trainer.device}")
    print(f"Physics-aware loss: {trainer.use_physics_loss}")
    print(f"Multi-step training: {trainer.multi_step_training}")
    
    # Create sample training data
    batch_size = 4
    grid_size = 32
    
    # Generate origin-centered training data
    train_inputs = []
    train_targets = []
    
    for _ in range(batch_size):
        initial = create_origin_centered_initial_condition(
            grid_size=(grid_size, grid_size, grid_size),
            n_sources=2,
            max_distance_from_origin=5
        )
        
        # Simulate one step
        solver = HeatDiffusion3D((grid_size, grid_size, grid_size))
        solver.set_state(initial)
        target = solver.step()
        
        train_inputs.append(initial)
        train_targets.append(target)
    
    train_inputs = torch.tensor(np.array(train_inputs), dtype=torch.float32).unsqueeze(1)
    train_targets = torch.tensor(np.array(train_targets), dtype=torch.float32).unsqueeze(1)
    
    print(f"Training data shape: {train_inputs.shape} -> {train_targets.shape}")
    
    # Test single training step
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    
    # Forward pass
    predictions = model(train_inputs)
    
    # Compute loss
    if trainer.use_physics_loss:
        loss = trainer.criterion(predictions, train_targets)
    else:
        loss = torch.nn.MSELoss()(predictions, train_targets)
    
    # Backward pass
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    
    print(f"Training loss: {loss.item():.6f}")
    
    # Compute physics metrics
    metrics = trainer.compute_physics_metrics(predictions, train_targets)
    print(f"Conservation error: {metrics['conservation_error']:.6f}")
    print(f"Relative L2 error: {metrics['relative_l2_error']:.6f}")
    print(f"Max error: {metrics['max_error']:.6f}")
    
    print("✅ Enhanced training demo completed\n")


def launch_fullscreen_ui():
    """Launch the full-screen physics sandbox UI."""
    print("🚀 Launching Full-Screen Physics Sandbox")
    print("=" * 50)
    
    # Create simulation engine
    engine = SimulationEngine(
        default_domain=PhysicsDomain.THERMODYNAMICS,
        parser_type="hybrid"
    )
    
    # Create full-screen UI
    ui = FullScreenPhysicsUI(simulation_engine=engine)
    
    print("Creating full-screen interface...")
    app = ui.create_interface()
    
    print("🌐 Launching web interface...")
    print("📍 Open your browser to the displayed URL")
    print("💡 Try commands like:")
    print("   - 'place a hot cube at the center'")
    print("   - 'add a sphere at origin'")
    print("   - 'create heat source at 0,0,0'")
    print("   - 'drop a ball from height 2 meters'")
    
    # Launch with custom settings
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=False,
        show_error=True
    )


def main():
    """Main demo function."""
    parser = argparse.ArgumentParser(description="Enhanced NeoPhysics Demo")
    parser.add_argument('--mode', choices=['all', 'network', 'physics', 'viz', 'training', 'ui'], 
                       default='all', help='Demo mode to run')
    parser.add_argument('--no-ui', action='store_true', help='Skip UI launch')
    
    args = parser.parse_args()
    
    print("🔥 Enhanced NeoPhysics Demo")
    print("=" * 60)
    print("Showcasing refined 3D physics sandbox with:")
    print("• Full-screen 3D environment with overlay UI")
    print("• Enhanced neural networks (FNO + Attention)")
    print("• Origin-centered object placement")
    print("• Advanced visualization features")
    print("• Physics-aware training pipeline")
    print("=" * 60)
    print()
    
    if args.mode in ['all', 'network']:
        demo_enhanced_neural_network()
    
    if args.mode in ['all', 'physics']:
        demo_origin_centered_physics()
    
    if args.mode in ['all', 'viz']:
        vol_fig, vec_fig, slice_fig = demo_advanced_visualization()
        
        # Optionally save visualizations
        print("💾 Saving visualization examples...")
        vol_fig.write_html("volume_rendering_demo.html")
        vec_fig.write_html("vector_field_demo.html")
        slice_fig.write_html("heat_map_slices_demo.html")
        print("Saved: volume_rendering_demo.html, vector_field_demo.html, heat_map_slices_demo.html")
    
    if args.mode in ['all', 'training']:
        demo_enhanced_training()
    
    if args.mode in ['all', 'ui'] and not args.no_ui:
        launch_fullscreen_ui()
    
    print("🎉 Demo completed successfully!")


if __name__ == "__main__":
    main()
