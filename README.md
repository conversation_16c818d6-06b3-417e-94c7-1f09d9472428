# NeoPhysics: 3D Generative Physics Sandbox

A 3D generative physics sandbox where users can interact with physics simulations using natural language.

## Features

- **Natural Language Interface**: Type commands like "drop a hot cube in the center" or "add wind from the left"
- **3D Physics Simulation**: Real-time 3D heat diffusion simulation on a 32×32×32 voxel grid
- **Machine Learning**: 3D U-Net model for predicting physics evolution
- **Interactive Visualization**: 3D volume rendering and animation controls

## Quick Start

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the demo:
```bash
python demo.py
```

## Project Structure

```
neophysics/
├── src/
│   ├── physics/          # Physics simulation engines
│   ├── models/           # ML models (3D U-Net, etc.)
│   ├── language/         # Natural language parsing
│   ├── visualization/    # 3D rendering and UI
│   └── utils/           # Utilities and helpers
├── data/                # Generated datasets
├── models/              # Trained model checkpoints
├── tests/               # Unit tests
└── examples/            # Example scripts and notebooks
```

## Development Phases

### Phase 1: Core Physics Backend ✅
- [x] 3D heat diffusion solver
- [x] Dataset generation
- [x] 3D U-Net training
- [x] Basic visualization

### Phase 2: Language Interface 🚧
- [ ] Natural language parser
- [ ] Integration layer
- [ ] Gradio web UI

### Phase 3: Advanced Features 📋
- [ ] WebGL visualization
- [ ] Enhanced language understanding
- [ ] Additional physics domains

## License

MIT License
