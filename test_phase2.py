#!/usr/bin/env python3
"""
Test script for Phase 2 functionality.
"""

import os
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_phase2_integration():
    """Test Phase 2 integration."""
    print('🧪 Testing Phase 2 Integration...')
    print('=' * 40)

    try:
        from src.core.simulation_engine import SimulationEngine
        from src.physics.base import PhysicsDomain
        
        # Create simulation engine
        print('✅ Creating simulation engine...')
        engine = SimulationEngine(
            default_domain=PhysicsDomain.KINEMATICS,
            parser_type='keyword'  # Use keyword parser to avoid LLM dependencies
        )
        
        # Test basic commands
        test_commands = [
            'create a ball with mass 5kg',
            'create a hill with height 10 meters',
            'set gravity to 9.8',
        ]
        
        print('✅ Testing natural language commands...')
        for cmd in test_commands:
            print(f'  Command: {cmd}')
            results = engine.execute_command(cmd)
            for result in results:
                if result.get('success'):
                    print(f'    ✅ Success: {result.get("message", "OK")}')
                else:
                    print(f'    ❌ Error: {result.get("error", "Failed")}')
        
        # Test analytics
        print('✅ Testing analytics...')
        analytics = engine.get_analytics()
        print(f'  Objects: {analytics["object_count"]}')
        print(f'  Domain: {analytics["physics_domain"]}')
        print(f'  Time: {analytics["simulation_time"]:.2f}s')
        
        print()
        print('🎉 Phase 2 integration test completed successfully!')
        return True
        
    except Exception as e:
        print(f'❌ Test failed: {e}')
        import traceback
        traceback.print_exc()
        return False


def test_physics_engines():
    """Test physics engine functionality."""
    print('\n🔬 Testing Physics Engines...')
    print('=' * 40)
    
    try:
        from src.physics.base import PhysicsEngineRegistry, PhysicsDomain
        from src.physics.kinematics import KinematicsEngine
        
        # Test engine registry
        print('✅ Testing engine registry...')
        available_domains = PhysicsEngineRegistry.get_available_domains()
        print(f'  Available domains: {[d.value for d in available_domains]}')
        
        # Test kinematics engine
        print('✅ Testing kinematics engine...')
        engine = KinematicsEngine()
        
        # Create objects
        ball = engine.create_ball(mass=5.0, radius=0.5, position=engine.world.objects)
        hill = engine.create_sloped_hill(height=10.0, slope_angle=30.0)
        
        print(f'  Created ball: {ball.name} (mass: {ball.mass}kg)')
        print(f'  Created hill: {hill.name}')
        
        # Run a few simulation steps
        print('✅ Testing simulation steps...')
        for i in range(5):
            success = engine.step()
            if success:
                print(f'  Step {i+1}: ✅')
            else:
                print(f'  Step {i+1}: ❌')
                break
        
        print('🎉 Physics engine test completed!')
        return True
        
    except Exception as e:
        print(f'❌ Physics test failed: {e}')
        import traceback
        traceback.print_exc()
        return False


def test_language_parsing():
    """Test language parsing functionality."""
    print('\n🗣️  Testing Language Parsing...')
    print('=' * 40)
    
    try:
        from src.language.parser import SimpleKeywordParser
        from src.language.llm_parser import create_parser
        
        # Test keyword parser
        print('✅ Testing keyword parser...')
        parser = SimpleKeywordParser()
        
        test_commands = [
            "create a ball with mass 5kg",
            "make a hill with height 10 meters",
            "set gravity to 9.8 m/s²",
            "run simulation for 5 seconds"
        ]
        
        for cmd in test_commands:
            print(f'  Parsing: "{cmd}"')
            commands = parser.parse(cmd)
            for command in commands:
                print(f'    → {command.action}: {command.parameters}')
        
        # Test hybrid parser (without LLM)
        print('✅ Testing hybrid parser...')
        hybrid_parser = create_parser("hybrid", use_llm_fallback=False)
        
        complex_cmd = "create a bouncy rubber ball with mass 3kg at the top of a steep hill"
        print(f'  Parsing complex: "{complex_cmd}"')
        commands = hybrid_parser.parse(complex_cmd)
        for command in commands:
            print(f'    → {command.action}: {command.parameters}')
        
        print('🎉 Language parsing test completed!')
        return True
        
    except Exception as e:
        print(f'❌ Language parsing test failed: {e}')
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print('🚀 NeoPhysics Phase 2 Test Suite')
    print('=' * 50)
    
    tests = [
        test_physics_engines,
        test_language_parsing,
        test_phase2_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f'❌ Test {test.__name__} crashed: {e}')
    
    print('\n' + '=' * 50)
    print(f'📊 Test Results: {passed}/{total} tests passed')
    
    if passed == total:
        print('🎉 All tests passed! Phase 2 is ready!')
        return 0
    else:
        print('❌ Some tests failed. Check the output above.')
        return 1


if __name__ == "__main__":
    exit(main())
