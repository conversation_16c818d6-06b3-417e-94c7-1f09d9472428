"""
Natural Language Parser for Physics Commands.

This module provides the foundation for parsing natural language commands
into structured physics actions that can be executed by the simulation engine.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import re
import json

from ..physics.base import PhysicsDomain, Vector3D


class CommandType(Enum):
    """Types of physics commands."""
    CREATE_OBJECT = "create_object"
    MODIFY_OBJECT = "modify_object"
    SET_PARAMETER = "set_parameter"
    RUN_SIMULATION = "run_simulation"
    ANALYZE_DATA = "analyze_data"
    RESET_SIMULATION = "reset_simulation"
    UNKNOWN = "unknown"


@dataclass
class PhysicsCommand:
    """Structured representation of a physics command."""
    command_type: CommandType
    domain: PhysicsDomain
    action: str
    parameters: Dict[str, Any]
    confidence: float = 1.0
    raw_text: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'command_type': self.command_type.value,
            'domain': self.domain.value,
            'action': self.action,
            'parameters': self.parameters,
            'confidence': self.confidence,
            'raw_text': self.raw_text
        }


class NLPParser(ABC):
    """Base class for natural language parsers."""
    
    def __init__(self):
        self.supported_domains = [PhysicsDomain.KINEMATICS]
        
    @abstractmethod
    def parse(self, text: str) -> List[PhysicsCommand]:
        """Parse natural language text into physics commands."""
        pass
    
    def extract_numbers(self, text: str) -> List[float]:
        """Extract numerical values from text."""
        # Pattern for numbers (including decimals and units)
        pattern = r'-?\d+\.?\d*'
        matches = re.findall(pattern, text)
        return [float(match) for match in matches]
    
    def extract_units(self, text: str) -> Dict[str, str]:
        """Extract units from text."""
        units = {}
        
        # Common physics units
        unit_patterns = {
            'mass': r'(\d+\.?\d*)\s*(kg|g|pounds?|lbs?)',
            'distance': r'(\d+\.?\d*)\s*(m|meters?|cm|mm|ft|feet|inches?)',
            'time': r'(\d+\.?\d*)\s*(s|seconds?|ms|minutes?|hrs?|hours?)',
            'force': r'(\d+\.?\d*)\s*(N|newtons?)',
            'velocity': r'(\d+\.?\d*)\s*(m/s|mph|km/h)',
            'acceleration': r'(\d+\.?\d*)\s*(m/s²|m/s2)'
        }
        
        for unit_type, pattern in unit_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                value, unit = matches[0]
                units[unit_type] = {'value': float(value), 'unit': unit}
        
        return units
    
    def extract_positions(self, text: str) -> List[Vector3D]:
        """Extract 3D positions from text."""
        positions = []
        
        # Look for coordinate patterns like (x,y,z) or "at position x,y,z"
        coord_patterns = [
            r'\((\d+\.?\d*),\s*(\d+\.?\d*),\s*(\d+\.?\d*)\)',
            r'position\s+(\d+\.?\d*),\s*(\d+\.?\d*),\s*(\d+\.?\d*)',
            r'at\s+(\d+\.?\d*),\s*(\d+\.?\d*),\s*(\d+\.?\d*)'
        ]
        
        for pattern in coord_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                x, y, z = map(float, match)
                positions.append(Vector3D(x, y, z))
        
        # Look for relative positions
        if 'center' in text.lower():
            positions.append(Vector3D(0, 0, 0))  # Will be adjusted by engine
        elif 'top' in text.lower():
            positions.append(Vector3D(0, 10, 0))  # Default high position
        elif 'left' in text.lower():
            positions.append(Vector3D(-5, 0, 0))
        elif 'right' in text.lower():
            positions.append(Vector3D(5, 0, 0))
        
        return positions
    
    def identify_objects(self, text: str) -> List[Dict[str, Any]]:
        """Identify objects mentioned in text."""
        objects = []
        
        # Object patterns
        object_patterns = {
            'ball': r'(ball|sphere)',
            'cube': r'(cube|box|block)',
            'cylinder': r'(cylinder|tube)',
            'hill': r'(hill|slope|ramp)',
            'ground': r'(ground|floor|terrain)'
        }
        
        for obj_type, pattern in object_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                objects.append({'type': obj_type, 'shape': obj_type})
        
        return objects
    
    def identify_materials(self, text: str) -> Dict[str, Any]:
        """Identify material properties from text."""
        materials = {}
        
        # Material keywords
        if any(word in text.lower() for word in ['bouncy', 'elastic', 'rubber']):
            materials['restitution'] = 0.8
        elif any(word in text.lower() for word in ['sticky', 'clay']):
            materials['restitution'] = 0.1
        
        if any(word in text.lower() for word in ['smooth', 'slippery', 'ice']):
            materials['friction'] = 0.1
        elif any(word in text.lower() for word in ['rough', 'sandpaper']):
            materials['friction'] = 0.9
        
        return materials


class SimpleKeywordParser(NLPParser):
    """Simple keyword-based parser for basic commands."""
    
    def __init__(self):
        super().__init__()
        
        # Command templates
        self.templates = {
            'create_ball': {
                'keywords': ['create', 'place', 'add', 'drop', 'ball', 'sphere'],
                'required': ['ball'],
                'command_type': CommandType.CREATE_OBJECT,
                'action': 'create_ball'
            },
            'create_hill': {
                'keywords': ['create', 'make', 'build', 'hill', 'slope', 'ramp'],
                'required': ['hill'],
                'command_type': CommandType.CREATE_OBJECT,
                'action': 'create_hill'
            },
            'set_gravity': {
                'keywords': ['set', 'change', 'gravity'],
                'required': ['gravity'],
                'command_type': CommandType.SET_PARAMETER,
                'action': 'set_gravity'
            },
            'run_simulation': {
                'keywords': ['run', 'start', 'simulate', 'begin'],
                'required': ['run'],
                'command_type': CommandType.RUN_SIMULATION,
                'action': 'run_simulation'
            }
        }
    
    def parse(self, text: str) -> List[PhysicsCommand]:
        """Parse text using keyword matching."""
        commands = []
        text_lower = text.lower()
        
        # Check each template
        for template_name, template in self.templates.items():
            if self._matches_template(text_lower, template):
                command = self._create_command_from_template(text, template)
                if command:
                    commands.append(command)
        
        # If no commands found, return unknown
        if not commands:
            commands.append(PhysicsCommand(
                command_type=CommandType.UNKNOWN,
                domain=PhysicsDomain.KINEMATICS,
                action="unknown",
                parameters={},
                confidence=0.0,
                raw_text=text
            ))
        
        return commands
    
    def _matches_template(self, text: str, template: Dict[str, Any]) -> bool:
        """Check if text matches a command template."""
        required_keywords = template['required']
        return all(keyword in text for keyword in required_keywords)
    
    def _create_command_from_template(self, text: str, template: Dict[str, Any]) -> Optional[PhysicsCommand]:
        """Create command from template and text."""
        action = template['action']
        parameters = {}
        
        # Extract parameters based on action type
        if action == 'create_ball':
            parameters = self._parse_create_ball(text)
        elif action == 'create_hill':
            parameters = self._parse_create_hill(text)
        elif action == 'set_gravity':
            parameters = self._parse_set_gravity(text)
        elif action == 'run_simulation':
            parameters = self._parse_run_simulation(text)
        
        return PhysicsCommand(
            command_type=template['command_type'],
            domain=PhysicsDomain.KINEMATICS,
            action=action,
            parameters=parameters,
            confidence=0.8,  # Keyword matching has medium confidence
            raw_text=text
        )
    
    def _parse_create_ball(self, text: str) -> Dict[str, Any]:
        """Parse ball creation parameters."""
        params = {}
        
        # Extract mass
        units = self.extract_units(text)
        if 'mass' in units:
            params['mass'] = units['mass']['value']
        else:
            # Look for numbers that might be mass
            numbers = self.extract_numbers(text)
            if numbers:
                params['mass'] = numbers[0]  # First number as mass
            else:
                params['mass'] = 1.0  # Default
        
        # Extract position
        positions = self.extract_positions(text)
        if positions:
            params['position'] = positions[0].to_array()
        else:
            params['position'] = [0, 5, 0]  # Default above ground
        
        # Extract size (radius)
        if 'radius' in text.lower():
            numbers = self.extract_numbers(text)
            if len(numbers) >= 2:
                params['radius'] = numbers[1]  # Second number as radius
        
        params.setdefault('radius', 0.5)  # Default radius
        
        # Extract material properties
        materials = self.identify_materials(text)
        if materials:
            params['material'] = materials
        
        return params
    
    def _parse_create_hill(self, text: str) -> Dict[str, Any]:
        """Parse hill creation parameters."""
        params = {}
        
        # Extract height
        units = self.extract_units(text)
        if 'distance' in units:
            params['height'] = units['distance']['value']
        else:
            numbers = self.extract_numbers(text)
            if numbers:
                params['height'] = numbers[0]
            else:
                params['height'] = 5.0  # Default height
        
        # Extract slope angle
        if 'angle' in text.lower() or 'degree' in text.lower():
            numbers = self.extract_numbers(text)
            if len(numbers) >= 2:
                params['slope_angle'] = numbers[1]
        
        params.setdefault('slope_angle', 30.0)  # Default 30 degrees
        
        return params
    
    def _parse_set_gravity(self, text: str) -> Dict[str, Any]:
        """Parse gravity setting parameters."""
        params = {}
        
        # Extract gravity value
        units = self.extract_units(text)
        if 'acceleration' in units:
            params['gravity'] = units['acceleration']['value']
        else:
            numbers = self.extract_numbers(text)
            if numbers:
                params['gravity'] = numbers[0]
            else:
                params['gravity'] = 9.81  # Default Earth gravity
        
        return params
    
    def _parse_run_simulation(self, text: str) -> Dict[str, Any]:
        """Parse simulation run parameters."""
        params = {}
        
        # Extract duration
        units = self.extract_units(text)
        if 'time' in units:
            params['duration'] = units['time']['value']
        else:
            numbers = self.extract_numbers(text)
            if numbers:
                params['duration'] = numbers[0]
            else:
                params['duration'] = 10.0  # Default 10 seconds
        
        return params
