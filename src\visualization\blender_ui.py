"""
Blender-like UI Framework using Gradio.

This module creates a web-based 3D interface similar to Blender, with:
- 3D viewport
- Property panels
- Timeline controls
- Natural language command input
- Real-time simulation controls
"""

import gradio as gr
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, List, Any, Optional, Tuple
import json
import time

# Import moved to avoid circular dependency
from ..physics.base import PhysicsDomain


class BlenderLikeUI:
    """Blender-like web interface for physics simulation."""
    
    def __init__(self, simulation_engine):
        """
        Initialize UI.
        
        Args:
            simulation_engine: main simulation engine
        """
        self.engine = simulation_engine
        self.app = None
        
        # UI state
        self.current_frame = 0
        self.max_frames = 300  # 5 seconds at 60 FPS
        self.playing = False
        self.viewport_data = None

        # UI components (will be initialized in create_interface)
        self.viewport_plot = None
        self.analytics_display = None
        self.object_list = None
        self.command_history_display = None
        self.execution_log_display = None
        self.timeline_slider = None
        self.time_display = None
        
        # Add frame callback to engine
        self.engine.add_frame_callback(self._on_simulation_frame)
        
        # Command history for UI
        self.command_history = []
        self.execution_log = []
    
    def create_interface(self) -> gr.Blocks:
        """Create the Gradio interface."""
        
        with gr.Blocks(title="NeoPhysics: 3D Generative Physics Sandbox", 
                      theme=gr.themes.Soft()) as app:
            
            # Header
            gr.Markdown("# 🔥 NeoPhysics: 3D Generative Physics Sandbox")
            gr.Markdown("*A Blender-like environment for physics simulation with natural language control*")
            
            with gr.Row():
                # Left Panel - Controls and Properties
                with gr.Column(scale=1, min_width=300):
                    self._create_control_panel()
                
                # Center - 3D Viewport
                with gr.Column(scale=2):
                    self._create_viewport()
                
                # Right Panel - Analytics and History
                with gr.Column(scale=1, min_width=300):
                    self._create_analytics_panel()
            
            # Bottom - Timeline and Playback Controls
            with gr.Row():
                self._create_timeline_controls()

        # Set up event handlers now that all components are created
        self._setup_event_handlers()

        self.app = app
        return app

    def _setup_event_handlers(self):
        """Set up event handlers after all components are created."""
        if hasattr(self, 'ui_components'):
            # Set up execute command handler
            execute_btn = self.ui_components['execute_btn']
            command_input = self.ui_components['command_input']

            execute_btn.click(
                self._execute_command,
                inputs=[command_input],
                outputs=self._get_output_components()
            )

            # Set up other handlers
            self.ui_components['domain_dropdown'].change(
                self._change_physics_domain,
                inputs=[self.ui_components['domain_dropdown']],
                outputs=[]
            )

            self.ui_components['gravity_slider'].change(
                self._update_gravity,
                inputs=[self.ui_components['gravity_slider']],
                outputs=[]
            )

    def _create_control_panel(self):
        """Create the left control panel."""
        with gr.Accordion("🎮 Simulation Controls", open=True):
            # Physics Domain Selection
            domain_dropdown = gr.Dropdown(
                choices=[domain.value for domain in PhysicsDomain],
                value=self.engine.default_domain.value,
                label="Physics Domain",
                info="Select the physics simulation domain"
            )
            
            # Natural Language Command Input
            command_input = gr.Textbox(
                label="Natural Language Command",
                placeholder="e.g., 'create a sloped hill that's 10 meters high and place a 5kg ball at the top'",
                lines=3
            )
            
            execute_btn = gr.Button("🚀 Execute Command", variant="primary")
            
            # Quick Actions
            with gr.Row():
                reset_btn = gr.Button("🔄 Reset", variant="secondary")
                play_btn = gr.Button("▶️ Play", variant="secondary")
                pause_btn = gr.Button("⏸️ Pause", variant="secondary")
        
        with gr.Accordion("⚙️ Physics Parameters", open=False):
            # Global physics parameters
            gravity_slider = gr.Slider(
                minimum=0, maximum=20, value=9.81, step=0.1,
                label="Gravity (m/s²)",
                info="Gravitational acceleration"
            )
            
            air_resistance_slider = gr.Slider(
                minimum=0, maximum=1, value=0.01, step=0.001,
                label="Air Resistance",
                info="Air resistance coefficient"
            )
            
            time_scale_slider = gr.Slider(
                minimum=0.1, maximum=5.0, value=1.0, step=0.1,
                label="Time Scale",
                info="Simulation speed multiplier"
            )
        
        with gr.Accordion("🎨 Visualization", open=False):
            # Visualization options
            show_wireframe = gr.Checkbox(label="Show Wireframes", value=False)
            show_forces = gr.Checkbox(label="Show Force Vectors", value=False)
            show_velocities = gr.Checkbox(label="Show Velocity Vectors", value=False)
            show_trails = gr.Checkbox(label="Show Object Trails", value=False)
        
        # Event handlers - set up after all components are created
        # We'll do this in a separate method called after interface creation
        
        # Store components for later reference
        self.ui_components = {
            'command_input': command_input,
            'domain_dropdown': domain_dropdown,
            'gravity_slider': gravity_slider,
            'air_resistance_slider': air_resistance_slider,
            'time_scale_slider': time_scale_slider,
            'execute_btn': execute_btn
        }
    
    def _create_viewport(self):
        """Create the 3D viewport."""
        gr.Markdown("### 🌐 3D Viewport")
        
        # 3D Plot using Plotly
        self.viewport_plot = gr.Plot(
            label="3D Scene",
            value=self._create_empty_scene(),
            show_label=False
        )
        
        # Viewport controls
        with gr.Row():
            view_front_btn = gr.Button("Front", size="sm")
            view_side_btn = gr.Button("Side", size="sm")
            view_top_btn = gr.Button("Top", size="sm")
            view_iso_btn = gr.Button("Isometric", size="sm")
            
        # Camera controls info
        gr.Markdown("*Use mouse to orbit, zoom, and pan the 3D view*")
    
    def _create_analytics_panel(self):
        """Create the right analytics panel."""
        with gr.Accordion("📊 Real-time Analytics", open=True):
            # Live data display
            self.analytics_display = gr.JSON(
                label="Simulation Data",
                value={}
            )
            
            # Object list
            self.object_list = gr.Dataframe(
                headers=["Name", "Type", "Mass", "Position", "Velocity"],
                datatype=["str", "str", "number", "str", "str"],
                label="Scene Objects"
            )
        
        with gr.Accordion("📝 Command History", open=False):
            self.command_history_display = gr.Textbox(
                label="Recent Commands",
                lines=10,
                max_lines=20,
                interactive=False
            )
        
        with gr.Accordion("🔍 Execution Log", open=False):
            self.execution_log_display = gr.Textbox(
                label="Execution Results",
                lines=8,
                max_lines=15,
                interactive=False
            )
    
    def _create_timeline_controls(self):
        """Create timeline and playback controls."""
        gr.Markdown("### ⏱️ Timeline")
        
        with gr.Row():
            # Timeline slider
            self.timeline_slider = gr.Slider(
                minimum=0, maximum=self.max_frames, value=0, step=1,
                label="Frame",
                info=f"Current frame (0-{self.max_frames})"
            )
            
            # Time display
            self.time_display = gr.Textbox(
                value="0.00s",
                label="Time",
                interactive=False,
                scale=0
            )
        
        with gr.Row():
            # Playback controls
            first_frame_btn = gr.Button("⏮️", size="sm")
            prev_frame_btn = gr.Button("⏪", size="sm")
            play_pause_btn = gr.Button("▶️", size="sm")
            next_frame_btn = gr.Button("⏩", size="sm")
            last_frame_btn = gr.Button("⏭️", size="sm")
            
            # Recording controls
            record_btn = gr.Button("🔴 Record", size="sm")
            export_btn = gr.Button("💾 Export", size="sm")
    
    def _create_empty_scene(self) -> go.Figure:
        """Create an empty 3D scene."""
        fig = go.Figure()
        
        # Add coordinate axes
        fig.add_trace(go.Scatter3d(
            x=[0, 5], y=[0, 0], z=[0, 0],
            mode='lines+text',
            line=dict(color='red', width=5),
            text=['', 'X'],
            name='X-axis'
        ))
        
        fig.add_trace(go.Scatter3d(
            x=[0, 0], y=[0, 5], z=[0, 0],
            mode='lines+text',
            line=dict(color='green', width=5),
            text=['', 'Y'],
            name='Y-axis'
        ))
        
        fig.add_trace(go.Scatter3d(
            x=[0, 0], y=[0, 0], z=[0, 5],
            mode='lines+text',
            line=dict(color='blue', width=5),
            text=['', 'Z'],
            name='Z-axis'
        ))
        
        # Configure layout
        fig.update_layout(
            scene=dict(
                xaxis_title='X (m)',
                yaxis_title='Y (m)',
                zaxis_title='Z (m)',
                aspectmode='cube',
                camera=dict(
                    eye=dict(x=1.5, y=1.5, z=1.5),
                    center=dict(x=0, y=0, z=0),
                    up=dict(x=0, y=1, z=0)
                )
            ),
            title="3D Physics Simulation",
            showlegend=False,
            margin=dict(l=0, r=0, t=30, b=0)
        )
        
        return fig
    
    def _execute_command(self, command_text: str) -> Tuple[Any, ...]:
        """Execute natural language command."""
        if not command_text.strip():
            return self._get_current_outputs()
        
        try:
            # Execute command
            results = self.engine.execute_command(command_text)
            
            # Update command history
            self.command_history.append(f"[{time.strftime('%H:%M:%S')}] {command_text}")
            
            # Update execution log
            for result in results:
                if result.get('success'):
                    self.execution_log.append(f"✅ {result.get('message', 'Command executed')}")
                else:
                    self.execution_log.append(f"❌ {result.get('error', 'Command failed')}")
            
            # Update viewport
            self._update_viewport()
            
            return self._get_current_outputs()
            
        except Exception as e:
            self.execution_log.append(f"❌ Error: {str(e)}")
            return self._get_current_outputs()
    
    def _change_physics_domain(self, domain_name: str):
        """Change physics domain."""
        try:
            domain = PhysicsDomain(domain_name)
            self.engine.switch_physics_domain(domain)
            self.execution_log.append(f"🔄 Switched to {domain_name} domain")
        except Exception as e:
            self.execution_log.append(f"❌ Failed to switch domain: {e}")
    
    def _update_gravity(self, gravity_value: float):
        """Update gravity parameter."""
        try:
            command_text = f"set gravity to {gravity_value}"
            self.engine.execute_command(command_text)
        except Exception as e:
            self.execution_log.append(f"❌ Failed to update gravity: {e}")
    
    def _update_viewport(self):
        """Update the 3D viewport with current scene."""
        try:
            fig = self._create_scene_plot()
            self.viewport_data = fig
        except Exception as e:
            print(f"Viewport update failed: {e}")
    
    def _create_scene_plot(self) -> go.Figure:
        """Create 3D plot from current scene."""
        fig = self._create_empty_scene()
        
        # Add physics objects to the plot
        for obj_name, scene_obj in self.engine.scene.objects.items():
            if scene_obj.physics_object:
                self._add_object_to_plot(fig, scene_obj)
        
        return fig
    
    def _add_object_to_plot(self, fig: go.Figure, scene_obj):
        """Add a scene object to the plot."""
        pos = scene_obj.transform.position
        physics_obj = scene_obj.physics_object
        
        if hasattr(physics_obj, 'shape'):
            if physics_obj.shape == "sphere":
                # Add sphere
                radius = physics_obj.size.x
                fig.add_trace(go.Scatter3d(
                    x=[pos.x], y=[pos.y], z=[pos.z],
                    mode='markers',
                    marker=dict(
                        size=radius * 20,  # Scale for visibility
                        color='red',
                        opacity=0.8
                    ),
                    name=f"Ball ({physics_obj.name})"
                ))
            
            elif physics_obj.shape == "cube":
                # Add cube (simplified as marker for now)
                fig.add_trace(go.Scatter3d(
                    x=[pos.x], y=[pos.y], z=[pos.z],
                    mode='markers',
                    marker=dict(
                        size=15,
                        color='green',
                        symbol='square',
                        opacity=0.8
                    ),
                    name=f"Cube ({physics_obj.name})"
                ))
        
        # Add terrain as surface
        if hasattr(physics_obj, 'height_function'):
            self._add_terrain_to_plot(fig, physics_obj)
    
    def _add_terrain_to_plot(self, fig: go.Figure, terrain_obj):
        """Add terrain to the plot."""
        # Create terrain surface
        x = np.linspace(-10, 10, 20)
        z = np.linspace(-10, 10, 20)
        X, Z = np.meshgrid(x, z)
        Y = np.zeros_like(X)
        
        # Apply height function
        for i in range(len(x)):
            for j in range(len(z)):
                Y[j, i] = terrain_obj.get_height_at(X[j, i], Z[j, i])
        
        fig.add_trace(go.Surface(
            x=X, y=Y, z=Z,
            colorscale='Earth',
            opacity=0.7,
            name="Terrain"
        ))
    
    def _get_current_outputs(self) -> Tuple[Any, ...]:
        """Get current UI outputs."""
        # Update analytics
        analytics = self.engine.get_analytics()
        
        # Update object list
        object_data = []
        for obj_name, scene_obj in self.engine.scene.objects.items():
            if scene_obj.physics_object:
                pos = scene_obj.transform.position
                vel = getattr(scene_obj.physics_object, 'velocity', None)
                mass = getattr(scene_obj.physics_object, 'mass', 'N/A')
                
                object_data.append([
                    obj_name,
                    getattr(scene_obj.physics_object, 'shape', 'unknown'),
                    mass,
                    f"({pos.x:.2f}, {pos.y:.2f}, {pos.z:.2f})",
                    f"({vel.x:.2f}, {vel.y:.2f}, {vel.z:.2f})" if vel else "N/A"
                ])
        
        # Format command history
        history_text = "\n".join(self.command_history[-10:])  # Last 10 commands
        
        # Format execution log
        log_text = "\n".join(self.execution_log[-15:])  # Last 15 entries
        
        return (
            self.viewport_data or self._create_empty_scene(),
            analytics,
            object_data,
            history_text,
            log_text
        )
    
    def _get_output_components(self) -> List[gr.Component]:
        """Get list of output components for event handlers."""
        components = []
        for comp in [self.viewport_plot, self.analytics_display, self.object_list,
                    self.command_history_display, self.execution_log_display]:
            if comp is not None:
                components.append(comp)
        return components
    
    def _on_simulation_frame(self, engine):
        """Callback for simulation frame updates."""
        # This would update the UI in real-time during simulation
        # For now, we'll just store the current frame
        self.current_frame += 1
        if self.current_frame > self.max_frames:
            self.current_frame = 0
    
    def launch(self, **kwargs):
        """Launch the Gradio interface."""
        if not self.app:
            self.app = self.create_interface()
        
        return self.app.launch(**kwargs)


# Example usage
if __name__ == "__main__":
    # Create simulation engine
    engine = SimulationEngine()
    
    # Create UI
    ui = BlenderLikeUI(engine)
    
    # Launch interface
    ui.launch(share=False, server_name="0.0.0.0", server_port=7860)
