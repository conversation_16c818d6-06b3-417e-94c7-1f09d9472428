"""
Main Simulation Engine that integrates physics, language parsing, and visualization.

This is the central orchestrator that brings together all components of the
3D Generative Physics Sandbox.
"""

from typing import Dict, List, Any, Optional, Callable
import logging
import time

from ..physics.base import PhysicsEngine, PhysicsEngineRegistry, PhysicsDomain
from ..language.parser import <PERSON><PERSON><PERSON><PERSON>, PhysicsCommand
from ..language.llm_parser import create_parser
from ..visualization.scene3d import Scene3D, SceneObject
from .command_executor import CommandExecutor


class SimulationEngine:
    """Main simulation engine that orchestrates all components."""
    
    def __init__(self, 
                 default_domain: PhysicsDomain = PhysicsDomain.KINEMATICS,
                 parser_type: str = "hybrid",
                 **parser_kwargs):
        """
        Initialize simulation engine.
        
        Args:
            default_domain: default physics domain
            parser_type: type of NLP parser ("keyword", "llm", "hybrid")
            **parser_kwargs: additional arguments for parser
        """
        self.default_domain = default_domain
        
        # Initialize components
        self.physics_engines: Dict[PhysicsDomain, PhysicsEngine] = {}
        self.current_engine: Optional[PhysicsEngine] = None
        self.scene = Scene3D()
        self.parser = create_parser(parser_type, **parser_kwargs)
        self.command_executor = CommandExecutor(self)
        
        # State
        self.running = False
        self.paused = False
        self.simulation_time = 0.0
        self.real_time_factor = 1.0  # 1.0 = real-time, 2.0 = 2x speed
        
        # Analytics and callbacks
        self.analytics_data: Dict[str, Any] = {}
        self.frame_callbacks: List[Callable] = []
        self.command_history: List[PhysicsCommand] = []
        
        # Initialize default physics engine
        self._initialize_default_engine()
        
        logging.info(f"SimulationEngine initialized with domain: {default_domain}")
    
    def _initialize_default_engine(self):
        """Initialize the default physics engine."""
        try:
            engine = PhysicsEngineRegistry.create_engine(self.default_domain)
            self.physics_engines[self.default_domain] = engine
            self.current_engine = engine
            logging.info(f"Initialized {self.default_domain} physics engine")
        except Exception as e:
            logging.error(f"Failed to initialize physics engine: {e}")
    
    def switch_physics_domain(self, domain: PhysicsDomain) -> bool:
        """
        Switch to a different physics domain.
        
        Args:
            domain: target physics domain
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if domain not in self.physics_engines:
                # Create new engine for this domain
                engine = PhysicsEngineRegistry.create_engine(domain)
                self.physics_engines[domain] = engine
            
            # Stop current engine
            if self.current_engine:
                self.current_engine.stop()
            
            # Switch to new engine
            self.current_engine = self.physics_engines[domain]
            self.default_domain = domain
            
            logging.info(f"Switched to {domain} physics domain")
            return True
            
        except Exception as e:
            logging.error(f"Failed to switch physics domain: {e}")
            return False
    
    def execute_command(self, text: str) -> List[Dict[str, Any]]:
        """
        Execute natural language command.
        
        Args:
            text: natural language command
            
        Returns:
            List of execution results
        """
        try:
            # Parse command
            commands = self.parser.parse(text)
            
            # Execute commands
            results = []
            for command in commands:
                result = self.command_executor.execute(command)
                results.append(result)
                
                # Store in history
                self.command_history.append(command)
            
            # Update scene from physics
            self._sync_scene_with_physics()
            
            return results
            
        except Exception as e:
            logging.error(f"Command execution failed: {e}")
            return [{'success': False, 'error': str(e)}]
    
    def _sync_scene_with_physics(self):
        """Synchronize 3D scene with physics objects."""
        if not self.current_engine:
            return
        
        # Get all physics objects
        physics_objects = self.current_engine.world.objects
        
        # Update existing scene objects
        for obj_id, physics_obj in physics_objects.items():
            scene_obj = self.scene.get_object(obj_id)
            
            if scene_obj is None:
                # Create new scene object
                scene_obj = self.scene.create_physics_object_mesh(physics_obj)
                self.scene.add_object(scene_obj, obj_id)
            else:
                # Update existing object
                scene_obj.update_from_physics()
        
        # Remove scene objects that no longer have physics counterparts
        scene_obj_names = list(self.scene.objects.keys())
        for obj_name in scene_obj_names:
            if obj_name not in physics_objects:
                self.scene.remove_object(obj_name)
    
    def start_simulation(self, duration: Optional[float] = None):
        """
        Start physics simulation.
        
        Args:
            duration: simulation duration in seconds (None for infinite)
        """
        if not self.current_engine:
            logging.error("No physics engine available")
            return
        
        self.running = True
        self.paused = False
        self.current_engine.start()
        
        logging.info(f"Started simulation (duration: {duration})")
        
        # Run simulation loop
        start_time = time.time()
        target_duration = duration or float('inf')
        
        while self.running and self.simulation_time < target_duration:
            if not self.paused:
                # Physics step
                success = self.current_engine.step()
                if not success:
                    break
                
                # Update scene
                self.scene.update(self.current_engine.world.dt)
                self._sync_scene_with_physics()
                
                # Update simulation time
                self.simulation_time = self.current_engine.world.time
                
                # Call frame callbacks
                for callback in self.frame_callbacks:
                    try:
                        callback(self)
                    except Exception as e:
                        logging.warning(f"Frame callback failed: {e}")
            
            # Real-time control
            if self.real_time_factor > 0:
                elapsed = time.time() - start_time
                target_time = self.simulation_time / self.real_time_factor
                if elapsed < target_time:
                    time.sleep(target_time - elapsed)
        
        self.running = False
        self.current_engine.stop()
        logging.info("Simulation stopped")
    
    def pause_simulation(self):
        """Pause simulation."""
        self.paused = True
        logging.info("Simulation paused")
    
    def resume_simulation(self):
        """Resume simulation."""
        self.paused = False
        logging.info("Simulation resumed")
    
    def stop_simulation(self):
        """Stop simulation."""
        self.running = False
        if self.current_engine:
            self.current_engine.stop()
        logging.info("Simulation stopped")
    
    def reset_simulation(self):
        """Reset simulation to initial state."""
        if self.current_engine:
            self.current_engine.reset()
        
        self.simulation_time = 0.0
        self.scene.current_time = 0.0
        self.analytics_data.clear()
        
        # Clear scene objects
        self.scene.objects.clear()
        
        logging.info("Simulation reset")
    
    def set_real_time_factor(self, factor: float):
        """
        Set real-time simulation speed factor.
        
        Args:
            factor: speed multiplier (1.0 = real-time, 0.5 = half-speed, 2.0 = double-speed)
        """
        self.real_time_factor = max(0.0, factor)
        logging.info(f"Real-time factor set to {factor}")
    
    def add_frame_callback(self, callback: Callable):
        """
        Add callback function to be called every simulation frame.
        
        Args:
            callback: function that takes SimulationEngine as argument
        """
        self.frame_callbacks.append(callback)
    
    def remove_frame_callback(self, callback: Callable):
        """Remove frame callback."""
        if callback in self.frame_callbacks:
            self.frame_callbacks.remove(callback)
    
    def get_analytics(self) -> Dict[str, Any]:
        """Get simulation analytics data."""
        analytics = {
            'simulation_time': self.simulation_time,
            'physics_domain': self.default_domain.value,
            'object_count': len(self.scene.objects),
            'command_history': [cmd.to_dict() for cmd in self.command_history[-10:]],  # Last 10 commands
        }
        
        # Add physics-specific analytics
        if self.current_engine:
            physics_analytics = self.current_engine.get_simulation_results()
            analytics.update(physics_analytics)
        
        return analytics
    
    def export_scene_data(self) -> Dict[str, Any]:
        """Export complete scene data for saving/loading."""
        return {
            'scene_name': self.scene.name,
            'physics_domain': self.default_domain.value,
            'simulation_time': self.simulation_time,
            'objects': {name: obj.physics_object.get_state() if obj.physics_object else {}
                       for name, obj in self.scene.objects.items()},
            'camera': {
                'position': self.scene.camera.position.to_array(),
                'target': self.scene.camera.target.to_array(),
                'fov': self.scene.camera.fov
            },
            'global_params': self.current_engine.world.global_params if self.current_engine else {}
        }
    
    def import_scene_data(self, scene_data: Dict[str, Any]):
        """Import scene data from saved file."""
        try:
            # Switch physics domain if needed
            domain = PhysicsDomain(scene_data['physics_domain'])
            if domain != self.default_domain:
                self.switch_physics_domain(domain)
            
            # Reset simulation
            self.reset_simulation()
            
            # Set global parameters
            if 'global_params' in scene_data and self.current_engine:
                for key, value in scene_data['global_params'].items():
                    self.current_engine.world.set_global_param(key, value)
            
            # Restore camera
            if 'camera' in scene_data:
                cam_data = scene_data['camera']
                self.scene.camera.position = Vector3D.from_array(cam_data['position'])
                self.scene.camera.target = Vector3D.from_array(cam_data['target'])
                self.scene.camera.fov = cam_data['fov']
            
            # Note: Object restoration would require more complex state management
            # This is a simplified version
            
            logging.info("Scene data imported successfully")
            
        except Exception as e:
            logging.error(f"Failed to import scene data: {e}")


# Example usage and testing
if __name__ == "__main__":
    # Create simulation engine
    engine = SimulationEngine()
    
    # Test commands
    test_commands = [
        "create a sloped hill that's 10 meters high",
        "place a 5kg ball at the top of the hill",
        "set gravity to 9.8 m/s²",
        "run simulation for 5 seconds"
    ]
    
    for cmd in test_commands:
        print(f"\nExecuting: {cmd}")
        results = engine.execute_command(cmd)
        for result in results:
            print(f"  Result: {result}")
    
    print(f"\nAnalytics: {engine.get_analytics()}")
