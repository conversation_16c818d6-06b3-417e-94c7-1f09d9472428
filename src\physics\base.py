"""
Base classes for extensible physics simulation architecture.

This module provides the foundation for multiple physics domains:
- Kinematics (rigid body dynamics)
- Electromagnetism (fields and charges)
- Quantum mechanics (wave functions)
- Thermodynamics (heat transfer)
- Fluid dynamics (Navier-Stokes)
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
from dataclasses import dataclass, field
from enum import Enum
import uuid


class PhysicsDomain(Enum):
    """Supported physics domains."""
    KINEMATICS = "kinematics"
    THERMODYNAMICS = "thermodynamics"
    ELECTROMAGNETISM = "electromagnetism"
    QUANTUM = "quantum"
    FLUID_DYNAMICS = "fluid_dynamics"


@dataclass
class Vector3D:
    """3D vector with common operations."""
    x: float = 0.0
    y: float = 0.0
    z: float = 0.0
    
    def __add__(self, other):
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other):
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar: float):
        return Vector3D(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def __rmul__(self, scalar: float):
        return self.__mul__(scalar)
    
    def magnitude(self) -> float:
        return np.sqrt(self.x**2 + self.y**2 + self.z**2)
    
    def normalize(self):
        mag = self.magnitude()
        if mag > 0:
            return Vector3D(self.x/mag, self.y/mag, self.z/mag)
        return Vector3D()
    
    def to_array(self) -> np.ndarray:
        return np.array([self.x, self.y, self.z])
    
    @classmethod
    def from_array(cls, arr: np.ndarray):
        return cls(float(arr[0]), float(arr[1]), float(arr[2]))


@dataclass
class Transform3D:
    """3D transformation (position, rotation, scale)."""
    position: Vector3D = field(default_factory=Vector3D)
    rotation: Vector3D = field(default_factory=Vector3D)  # Euler angles in radians
    scale: Vector3D = field(default_factory=lambda: Vector3D(1, 1, 1))


class PhysicsObject(ABC):
    """Base class for all physics objects."""
    
    def __init__(self, 
                 name: str,
                 object_id: str = None,
                 transform: Transform3D = None):
        self.name = name
        self.id = object_id or str(uuid.uuid4())
        self.transform = transform or Transform3D()
        self.properties: Dict[str, Any] = {}
        self.active = True
        
    @abstractmethod
    def update(self, dt: float, world_state: Dict[str, Any]):
        """Update object state for one time step."""
        pass
    
    @abstractmethod
    def get_state(self) -> Dict[str, Any]:
        """Get current object state."""
        pass
    
    @abstractmethod
    def set_state(self, state: Dict[str, Any]):
        """Set object state."""
        pass
    
    def get_position(self) -> Vector3D:
        return self.transform.position
    
    def set_position(self, position: Vector3D):
        self.transform.position = position


class PhysicsWorld:
    """Container for physics objects and global parameters."""
    
    def __init__(self, domain: PhysicsDomain):
        self.domain = domain
        self.objects: Dict[str, PhysicsObject] = {}
        self.global_params: Dict[str, Any] = {}
        self.time = 0.0
        self.dt = 0.016  # Default 60 FPS
        
        # Analytics tracking
        self.analytics_enabled = True
        self.analytics_data: Dict[str, List[Any]] = {}
        
    def add_object(self, obj: PhysicsObject):
        """Add object to the world."""
        self.objects[obj.id] = obj
        
    def remove_object(self, object_id: str):
        """Remove object from the world."""
        if object_id in self.objects:
            del self.objects[object_id]
    
    def get_object(self, object_id: str) -> Optional[PhysicsObject]:
        """Get object by ID."""
        return self.objects.get(object_id)
    
    def get_objects_by_name(self, name: str) -> List[PhysicsObject]:
        """Get all objects with given name."""
        return [obj for obj in self.objects.values() if obj.name == name]
    
    def set_global_param(self, key: str, value: Any):
        """Set global physics parameter."""
        self.global_params[key] = value
    
    def get_global_param(self, key: str, default: Any = None) -> Any:
        """Get global physics parameter."""
        return self.global_params.get(key, default)
    
    def get_world_state(self) -> Dict[str, Any]:
        """Get complete world state."""
        return {
            'time': self.time,
            'dt': self.dt,
            'global_params': self.global_params.copy(),
            'objects': {obj_id: obj.get_state() for obj_id, obj in self.objects.items()}
        }
    
    def collect_analytics(self):
        """Collect analytics data from all objects."""
        if not self.analytics_enabled:
            return
            
        timestamp = self.time
        for obj_id, obj in self.objects.items():
            if obj_id not in self.analytics_data:
                self.analytics_data[obj_id] = []
            
            # Collect basic data
            data_point = {
                'time': timestamp,
                'position': obj.get_position().to_array(),
                'properties': obj.properties.copy()
            }
            
            self.analytics_data[obj_id].append(data_point)


class PhysicsEngine(ABC):
    """Base class for physics engines."""
    
    def __init__(self, domain: PhysicsDomain):
        self.domain = domain
        self.world = PhysicsWorld(domain)
        self.running = False
        
    @abstractmethod
    def step(self) -> bool:
        """Perform one simulation step. Returns True if successful."""
        pass
    
    @abstractmethod
    def reset(self):
        """Reset simulation to initial state."""
        pass
    
    def start(self):
        """Start simulation."""
        self.running = True
    
    def stop(self):
        """Stop simulation."""
        self.running = False
    
    def run_simulation(self, duration: float, dt: Optional[float] = None) -> Dict[str, Any]:
        """Run simulation for specified duration."""
        if dt:
            self.world.dt = dt
            
        steps = int(duration / self.world.dt)
        
        for _ in range(steps):
            if not self.step():
                break
                
        return self.get_simulation_results()
    
    def get_simulation_results(self) -> Dict[str, Any]:
        """Get complete simulation results including analytics."""
        return {
            'final_state': self.world.get_world_state(),
            'analytics': self.world.analytics_data.copy(),
            'domain': self.domain.value,
            'total_time': self.world.time
        }


class PhysicsEngineRegistry:
    """Registry for different physics engines."""
    
    _engines: Dict[PhysicsDomain, type] = {}
    
    @classmethod
    def register(cls, domain: PhysicsDomain, engine_class: type):
        """Register a physics engine for a domain."""
        cls._engines[domain] = engine_class
    
    @classmethod
    def create_engine(cls, domain: PhysicsDomain, **kwargs) -> PhysicsEngine:
        """Create physics engine for specified domain."""
        if domain not in cls._engines:
            raise ValueError(f"No engine registered for domain: {domain}")
        
        return cls._engines[domain](**kwargs)
    
    @classmethod
    def get_available_domains(cls) -> List[PhysicsDomain]:
        """Get list of available physics domains."""
        return list(cls._engines.keys())


# Utility functions for common physics operations
def calculate_distance(pos1: Vector3D, pos2: Vector3D) -> float:
    """Calculate distance between two positions."""
    diff = pos2 - pos1
    return diff.magnitude()


def apply_gravity(position: Vector3D, mass: float, gravity: Vector3D) -> Vector3D:
    """Apply gravitational force."""
    return gravity * mass


def clamp(value: float, min_val: float, max_val: float) -> float:
    """Clamp value between min and max."""
    return max(min_val, min(max_val, value))
