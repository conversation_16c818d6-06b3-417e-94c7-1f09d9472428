"""
Advanced 3D Visualization Features for Physics Simulation.

This module provides sophisticated visualization capabilities including:
- Volume rendering for scalar fields
- Vector field visualization (arrows, streamlines)
- Particle systems and traces
- Heat maps and color mapping
- Advanced rendering techniques
"""

import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Any, Optional, Tuple, Union
# Optional dependencies
try:
    from scipy.ndimage import gaussian_filter
except ImportError:
    gaussian_filter = None


class AdvancedPhysicsVisualizer:
    """Advanced visualization system for physics simulations."""
    
    def __init__(self):
        """Initialize the advanced visualizer."""
        self.color_scales = {
            'temperature': 'plasma',
            'velocity': 'viridis',
            'pressure': 'RdBu',
            'density': 'Blues',
            'energy': 'Inferno'
        }
        
        self.default_camera = dict(
            eye=dict(x=1.5, y=1.5, z=1.5),
            center=dict(x=0, y=0, z=0),
            up=dict(x=0, y=0, z=1)
        )
    
    def create_volume_rendering(self, 
                              field_data: np.ndarray,
                              field_type: str = 'temperature',
                              opacity: float = 0.1,
                              show_isosurfaces: bool = True,
                              num_isosurfaces: int = 5) -> go.Figure:
        """
        Create volume rendering of a 3D scalar field.
        
        Args:
            field_data: 3D numpy array of scalar field values
            field_type: type of field for color mapping
            opacity: volume opacity
            show_isosurfaces: whether to show isosurfaces
            num_isosurfaces: number of isosurfaces to display
            
        Returns:
            Plotly figure with volume rendering
        """
        fig = go.Figure()
        
        # Ensure field_data is centered at origin
        nx, ny, nz = field_data.shape
        x = np.linspace(-nx//2, nx//2, nx)
        y = np.linspace(-ny//2, ny//2, ny)
        z = np.linspace(-nz//2, nz//2, nz)
        
        X, Y, Z = np.meshgrid(x, y, z, indexing='ij')
        
        # Volume rendering
        fig.add_trace(go.Volume(
            x=X.flatten(),
            y=Y.flatten(),
            z=Z.flatten(),
            value=field_data.flatten(),
            opacity=opacity,
            surface_count=15,
            colorscale=self.color_scales.get(field_type, 'viridis'),
            name=f'{field_type.title()} Field'
        ))
        
        # Add isosurfaces
        if show_isosurfaces:
            field_min, field_max = field_data.min(), field_data.max()
            iso_values = np.linspace(field_min + 0.1*(field_max-field_min), 
                                   field_max - 0.1*(field_max-field_min), 
                                   num_isosurfaces)
            
            for i, iso_val in enumerate(iso_values):
                fig.add_trace(go.Isosurface(
                    x=X.flatten(),
                    y=Y.flatten(),
                    z=Z.flatten(),
                    value=field_data.flatten(),
                    isomin=iso_val,
                    isomax=iso_val,
                    opacity=0.3,
                    colorscale=self.color_scales.get(field_type, 'viridis'),
                    showscale=False,
                    name=f'Isosurface {iso_val:.2f}'
                ))
        
        # Update layout
        fig.update_layout(
            scene=dict(
                xaxis_title="X",
                yaxis_title="Y", 
                zaxis_title="Z",
                camera=self.default_camera,
                aspectmode='cube'
            ),
            title=f"Volume Rendering: {field_type.title()} Field",
            margin=dict(l=0, r=0, t=40, b=0)
        )
        
        return fig
    
    def create_vector_field_visualization(self,
                                        positions: np.ndarray,
                                        vectors: np.ndarray,
                                        field_type: str = 'velocity',
                                        arrow_scale: float = 1.0,
                                        subsample_factor: int = 2) -> go.Figure:
        """
        Create vector field visualization with arrows.
        
        Args:
            positions: (N, 3) array of positions
            vectors: (N, 3) array of vectors at each position
            field_type: type of vector field
            arrow_scale: scaling factor for arrow size
            subsample_factor: factor to subsample vectors for clarity
            
        Returns:
            Plotly figure with vector field
        """
        fig = go.Figure()
        
        # Subsample for clarity
        indices = np.arange(0, len(positions), subsample_factor)
        pos_sub = positions[indices]
        vec_sub = vectors[indices]
        
        # Calculate vector magnitudes for coloring
        magnitudes = np.linalg.norm(vec_sub, axis=1)
        
        # Create arrow traces
        for i in range(len(pos_sub)):
            start = pos_sub[i]
            end = start + vec_sub[i] * arrow_scale
            
            # Arrow shaft
            fig.add_trace(go.Scatter3d(
                x=[start[0], end[0]],
                y=[start[1], end[1]],
                z=[start[2], end[2]],
                mode='lines',
                line=dict(
                    color=magnitudes[i],
                    colorscale=self.color_scales.get(field_type, 'viridis'),
                    width=3
                ),
                showlegend=False,
                hovertemplate=f'Magnitude: {magnitudes[i]:.3f}<extra></extra>'
            ))
        
        # Add origin marker
        fig.add_trace(go.Scatter3d(
            x=[0], y=[0], z=[0],
            mode='markers',
            marker=dict(size=8, color='red'),
            name='Origin (0,0,0)'
        ))
        
        fig.update_layout(
            scene=dict(
                xaxis_title="X",
                yaxis_title="Y",
                zaxis_title="Z",
                camera=self.default_camera,
                aspectmode='cube'
            ),
            title=f"Vector Field: {field_type.title()}",
            margin=dict(l=0, r=0, t=40, b=0)
        )
        
        return fig
    
    def create_particle_system(self,
                             particle_positions: List[np.ndarray],
                             particle_properties: Optional[List[np.ndarray]] = None,
                             show_traces: bool = True,
                             trace_length: int = 50) -> go.Figure:
        """
        Create particle system visualization with optional traces.
        
        Args:
            particle_positions: List of position arrays for each time step
            particle_properties: Optional properties (color, size) for each particle
            show_traces: whether to show particle traces
            trace_length: length of particle traces
            
        Returns:
            Plotly figure with particle system
        """
        fig = go.Figure()
        
        if not particle_positions:
            return fig
        
        num_particles = particle_positions[0].shape[0]
        num_timesteps = len(particle_positions)
        
        # Show current particle positions
        current_pos = particle_positions[-1]
        
        if particle_properties is not None and len(particle_properties) > 0:
            colors = particle_properties[-1]
        else:
            colors = np.arange(num_particles)
        
        fig.add_trace(go.Scatter3d(
            x=current_pos[:, 0],
            y=current_pos[:, 1],
            z=current_pos[:, 2],
            mode='markers',
            marker=dict(
                size=5,
                color=colors,
                colorscale='viridis',
                opacity=0.8
            ),
            name='Particles'
        ))
        
        # Add particle traces
        if show_traces and num_timesteps > 1:
            start_idx = max(0, num_timesteps - trace_length)
            
            for particle_id in range(num_particles):
                trace_x = []
                trace_y = []
                trace_z = []
                
                for t in range(start_idx, num_timesteps):
                    if particle_id < particle_positions[t].shape[0]:
                        trace_x.append(particle_positions[t][particle_id, 0])
                        trace_y.append(particle_positions[t][particle_id, 1])
                        trace_z.append(particle_positions[t][particle_id, 2])
                
                if len(trace_x) > 1:
                    fig.add_trace(go.Scatter3d(
                        x=trace_x,
                        y=trace_y,
                        z=trace_z,
                        mode='lines',
                        line=dict(
                            color=f'rgba(100, 150, 200, 0.5)',
                            width=2
                        ),
                        showlegend=False,
                        hoverinfo='skip'
                    ))
        
        # Add origin
        fig.add_trace(go.Scatter3d(
            x=[0], y=[0], z=[0],
            mode='markers',
            marker=dict(size=8, color='red'),
            name='Origin (0,0,0)'
        ))
        
        fig.update_layout(
            scene=dict(
                xaxis_title="X",
                yaxis_title="Y",
                zaxis_title="Z",
                camera=self.default_camera,
                aspectmode='cube'
            ),
            title="Particle System with Traces",
            margin=dict(l=0, r=0, t=40, b=0)
        )
        
        return fig
    
    def create_heat_map_slices(self,
                             field_data: np.ndarray,
                             slice_positions: Optional[List[int]] = None,
                             field_type: str = 'temperature') -> go.Figure:
        """
        Create heat map slices through a 3D field.
        
        Args:
            field_data: 3D numpy array
            slice_positions: positions for slices in each dimension
            field_type: type of field for labeling
            
        Returns:
            Plotly figure with heat map slices
        """
        nx, ny, nz = field_data.shape
        
        if slice_positions is None:
            slice_positions = [nx//2, ny//2, nz//2]
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=['XY Slice', 'XZ Slice', 'YZ Slice', '3D Overview'],
            specs=[[{'type': 'heatmap'}, {'type': 'heatmap'}],
                   [{'type': 'heatmap'}, {'type': 'scatter3d'}]]
        )
        
        # XY slice (at z = slice_positions[2])
        xy_slice = field_data[:, :, slice_positions[2]]
        fig.add_trace(
            go.Heatmap(
                z=xy_slice,
                colorscale=self.color_scales.get(field_type, 'viridis'),
                showscale=False
            ),
            row=1, col=1
        )
        
        # XZ slice (at y = slice_positions[1])
        xz_slice = field_data[:, slice_positions[1], :]
        fig.add_trace(
            go.Heatmap(
                z=xz_slice,
                colorscale=self.color_scales.get(field_type, 'viridis'),
                showscale=False
            ),
            row=1, col=2
        )
        
        # YZ slice (at x = slice_positions[0])
        yz_slice = field_data[slice_positions[0], :, :]
        fig.add_trace(
            go.Heatmap(
                z=yz_slice,
                colorscale=self.color_scales.get(field_type, 'viridis'),
                showscale=True
            ),
            row=2, col=1
        )
        
        # 3D overview with slice planes
        x = np.linspace(-nx//2, nx//2, nx)
        y = np.linspace(-ny//2, ny//2, ny)
        z = np.linspace(-nz//2, nz//2, nz)
        
        # Add slice plane indicators
        fig.add_trace(
            go.Scatter3d(
                x=[x[slice_positions[0]]]*4,
                y=[y[0], y[-1], y[-1], y[0]],
                z=[z[0], z[0], z[-1], z[-1]],
                mode='lines',
                line=dict(color='red', width=5),
                name='YZ Slice Plane'
            ),
            row=2, col=2
        )
        
        fig.update_layout(
            title=f"{field_type.title()} Field Slices",
            height=800
        )
        
        return fig
