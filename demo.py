#!/usr/bin/env python3
"""
Demo script for the 3D Generative Physics Sandbox.

This script demonstrates the core functionality:
1. Generate training data
2. Train a 3D U-Net model
3. Test predictions and visualize results
"""

import os
import sys
import torch
import numpy as np
import argparse

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.physics.heat_diffusion import HeatDiffusion3D, create_random_initial_condition
from src.utils.dataset import PhysicsDataset
from src.models.unet3d import PhysicsPredictor
from src.models.trainer import PhysicsTrainer, prepare_datasets, PhysicsDatasetTorch
from src.visualization.basic_viz import (
    plot_2d_slices, plot_3d_isosurface, animate_simulation, compare_predictions
)


def demo_physics_simulation():
    """Demonstrate basic physics simulation."""
    print("=== Physics Simulation Demo ===")
    
    # Create solver
    solver = HeatDiffusion3D(grid_size=(32, 32, 32), dt=0.01, alpha=0.1)
    
    # Add some heat sources
    solver.add_heat_source((16, 16, 16), shape="cube", size=3, temperature=100.0)
    solver.add_heat_source((8, 8, 8), shape="sphere", size=2, temperature=80.0)
    solver.add_heat_source((24, 24, 24), shape="cube", size=2, temperature=120.0)
    
    print("Initial temperature field created with 3 heat sources")
    
    # Run simulation
    n_steps = 30
    history = solver.simulate(n_steps)
    
    print(f"Simulation completed: {n_steps} steps")
    print(f"Temperature range: {history.min():.2f} to {history.max():.2f}")
    
    # Visualize initial and final states
    plot_2d_slices(history[0], title="Initial State")
    plot_2d_slices(history[-1], title="Final State")
    
    # Create animation
    print("Creating animation...")
    animate_simulation(history, title="Heat Diffusion Simulation")
    
    return history


def demo_dataset_generation():
    """Demonstrate dataset generation."""
    print("\n=== Dataset Generation Demo ===")
    
    # Create dataset generator
    dataset_gen = PhysicsDataset(grid_size=(32, 32, 32))
    
    # Generate small dataset for demo
    print("Generating training data...")
    inputs, targets = dataset_gen.generate_training_pairs(
        n_sequences=50,  # Small for demo
        n_steps=20,
        save_path="data/demo_dataset.h5"
    )
    
    print(f"Dataset generated: {len(inputs)} training pairs")
    print(f"Input shape: {inputs.shape}")
    print(f"Target shape: {targets.shape}")
    
    return inputs, targets


def demo_model_training(inputs, targets):
    """Demonstrate model training."""
    print("\n=== Model Training Demo ===")
    
    # Create PyTorch datasets
    full_dataset = PhysicsDatasetTorch(inputs, targets)
    
    # Split into train/val
    n_val = len(full_dataset) // 5  # 20% validation
    n_train = len(full_dataset) - n_val
    train_dataset, val_dataset = torch.utils.data.random_split(full_dataset, [n_train, n_val])
    
    print(f"Train samples: {len(train_dataset)}")
    print(f"Val samples: {len(val_dataset)}")
    
    # Create model and trainer
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Training on: {device}")
    
    model = PhysicsPredictor(grid_size=(32, 32, 32), use_residual=True)
    trainer = PhysicsTrainer(model, device)
    
    n_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model has {n_params:,} trainable parameters")
    
    # Train model (short training for demo)
    print("Starting training...")
    history = trainer.train(
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        n_epochs=10,  # Short for demo
        batch_size=4,
        learning_rate=1e-3,
        save_path="models/demo_model.pth"
    )
    
    # Plot training history
    trainer.plot_training_history()
    
    return model, trainer


def demo_model_prediction(model, trainer):
    """Demonstrate model predictions."""
    print("\n=== Model Prediction Demo ===")
    
    # Create test simulation
    solver = HeatDiffusion3D(grid_size=(32, 32, 32))
    
    # Add test heat sources
    solver.add_heat_source((10, 10, 10), shape="sphere", size=3, temperature=100.0)
    solver.add_heat_source((22, 22, 22), shape="cube", size=2, temperature=80.0)
    
    initial_state = solver.get_state()
    
    # Ground truth simulation
    print("Running ground truth simulation...")
    gt_history = solver.simulate(20)
    
    # Model prediction
    print("Running model prediction...")
    model.eval()
    device = trainer.device
    
    pred_history = [initial_state]
    current_state = torch.from_numpy(initial_state).float().unsqueeze(0).to(device)
    
    with torch.no_grad():
        for step in range(20):
            next_state = model(current_state)
            pred_state = next_state.cpu().numpy()[0]
            pred_history.append(pred_state)
            current_state = next_state
    
    pred_history = np.array(pred_history)
    
    print("Prediction completed")
    
    # Compare results
    compare_predictions(
        gt_history, pred_history, 
        time_step=10, 
        title="Ground Truth vs Model Prediction (Step 10)"
    )
    
    compare_predictions(
        gt_history, pred_history, 
        time_step=-1, 
        title="Ground Truth vs Model Prediction (Final)"
    )
    
    # Create side-by-side animation would be nice but complex for demo
    print("Creating prediction animation...")
    animate_simulation(pred_history, title="Model Prediction")
    
    return gt_history, pred_history


def main():
    """Main demo function."""
    parser = argparse.ArgumentParser(description="3D Physics Sandbox Demo")
    parser.add_argument("--skip-training", action="store_true", 
                       help="Skip training and load existing model")
    parser.add_argument("--skip-physics", action="store_true",
                       help="Skip physics simulation demo")
    parser.add_argument("--skip-dataset", action="store_true",
                       help="Skip dataset generation demo")
    
    args = parser.parse_args()
    
    print("🔥 3D Generative Physics Sandbox Demo 🔥")
    print("=" * 50)
    
    # Create directories
    os.makedirs("data", exist_ok=True)
    os.makedirs("models", exist_ok=True)
    
    # Demo 1: Physics simulation
    if not args.skip_physics:
        history = demo_physics_simulation()
    
    # Demo 2: Dataset generation
    if not args.skip_dataset:
        inputs, targets = demo_dataset_generation()
    else:
        # Load existing dataset
        if os.path.exists("data/demo_dataset.h5"):
            from src.utils.dataset import PhysicsDataset
            inputs, targets, _ = PhysicsDataset.load_dataset("data/demo_dataset.h5")
            print("Loaded existing dataset")
        else:
            print("No existing dataset found, generating...")
            inputs, targets = demo_dataset_generation()
    
    # Demo 3: Model training
    if not args.skip_training:
        model, trainer = demo_model_training(inputs, targets)
    else:
        # Load existing model
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = PhysicsPredictor(grid_size=(32, 32, 32))
        trainer = PhysicsTrainer(model, device)
        
        if os.path.exists("models/demo_model.pth"):
            trainer.load_model("models/demo_model.pth")
            print("Loaded existing model")
        else:
            print("No existing model found, training...")
            model, trainer = demo_model_training(inputs, targets)
    
    # Demo 4: Model prediction
    gt_history, pred_history = demo_model_prediction(model, trainer)
    
    print("\n🎉 Demo completed successfully!")
    print("\nNext steps:")
    print("1. Implement natural language parsing")
    print("2. Create web interface with Gradio")
    print("3. Add more physics domains")
    print("4. Enhance visualization with WebGL")


if __name__ == "__main__":
    main()
