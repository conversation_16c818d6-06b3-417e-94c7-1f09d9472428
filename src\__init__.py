# NeoPhysics: 3D Generative Physics Sandbox

from .core.simulation_engine import SimulationEngine
from .physics.base import PhysicsDomain, PhysicsEngineRegistry, ensure_origin_based_position
from .language.parser import NLPParser, PhysicsCommand
from .language.llm_parser import create_parser
from .visualization.blender_ui import BlenderLikeUI
from .models.enhanced_networks import EnhancedPhysicsNet, PhysicsAwareLoss
from .models.enhanced_trainer import EnhancedPhysicsTrainer

__version__ = "0.3.0"
__author__ = "NeoPhysics Team"
__description__ = "Enhanced 3D Generative Physics Sandbox with Full-Screen UI and Advanced Neural Networks"
