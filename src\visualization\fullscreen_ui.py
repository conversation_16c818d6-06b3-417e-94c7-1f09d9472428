"""
Full-Screen 3D Physics Sandbox Interface.

This module creates a professional full-screen 3D interface with overlay UI panels,
designed for immersive physics simulation and visualization.
"""

import gradio as gr
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, List, Any, Optional, Tuple
import json
import time

from ..physics.base import PhysicsDomain


class FullScreenPhysicsUI:
    """Full-screen 3D physics sandbox interface with overlay controls."""
    
    def __init__(self, simulation_engine = None):
        """Initialize the full-screen UI."""
        self.simulation_engine = simulation_engine
        self.ui_components = {}
        self.current_scene_data = None
        self.animation_frames = []
        self.is_playing = False
        self.current_frame = 0
        
        # UI state
        self.show_control_panel = True
        self.show_properties_panel = True
        self.show_timeline = True
        
    def create_interface(self) -> gr.Blocks:
        """Create the full-screen Gradio interface."""
        
        # Custom CSS for full-screen layout with overlays
        custom_css = """
        .main-container {
            height: 100vh !important;
            width: 100vw !important;
            margin: 0 !important;
            padding: 0 !important;
            position: relative;
        }
        
        .viewport-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100% !important;
            height: 100% !important;
            z-index: 1;
        }
        
        .overlay-panel {
            position: absolute;
            background: rgba(30, 30, 30, 0.9);
            border-radius: 8px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 10;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .control-panel {
            top: 20px;
            left: 20px;
            width: 300px;
        }
        
        .properties-panel {
            top: 20px;
            right: 20px;
            width: 280px;
        }
        
        .timeline-panel {
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 120px;
        }
        
        .command-panel {
            bottom: 160px;
            left: 50%;
            transform: translateX(-50%);
            width: 50%;
        }
        
        .plotly-graph-div {
            height: 100vh !important;
        }
        """
        
        with gr.Blocks(
            title="NeoPhysics: Full-Screen 3D Physics Sandbox",
            theme=gr.themes.Soft(),
            css=custom_css
        ) as app:
            
            # Main container
            with gr.Row(elem_classes="main-container"):
                
                # Full-screen 3D viewport
                with gr.Column(elem_classes="viewport-container"):
                    self.ui_components['viewport'] = gr.Plot(
                        label="",
                        show_label=False,
                        container=True,
                        elem_classes="viewport"
                    )
                
                # Overlay panels
                self._create_overlay_panels()
            
            # Set up event handlers
            self._setup_event_handlers()
            
        self.app = app
        return app
    
    def _create_overlay_panels(self):
        """Create overlay UI panels."""
        
        # Control Panel (Top Left)
        with gr.Column(elem_classes="overlay-panel control-panel", visible=True) as control_panel:
            gr.Markdown("### 🎮 Simulation Controls")
            
            # Physics domain selection
            self.ui_components['domain_dropdown'] = gr.Dropdown(
                choices=[domain.value for domain in PhysicsDomain],
                value=PhysicsDomain.THERMODYNAMICS.value,
                label="Physics Domain",
                interactive=True
            )
            
            # Simulation parameters
            with gr.Accordion("Parameters", open=True):
                self.ui_components['gravity_slider'] = gr.Slider(
                    minimum=0, maximum=20, value=9.81, step=0.1,
                    label="Gravity (m/s²)"
                )
                
                self.ui_components['time_step'] = gr.Number(
                    value=0.01, label="Time Step (s)", precision=4
                )
                
                self.ui_components['grid_size'] = gr.Slider(
                    minimum=16, maximum=128, value=64, step=16,
                    label="Grid Resolution"
                )
            
            # Simulation controls
            with gr.Row():
                self.ui_components['play_btn'] = gr.Button("▶️ Play", variant="primary")
                self.ui_components['pause_btn'] = gr.Button("⏸️ Pause")
                self.ui_components['reset_btn'] = gr.Button("🔄 Reset")
            
        self.ui_components['control_panel'] = control_panel
        
        # Properties Panel (Top Right)
        with gr.Column(elem_classes="overlay-panel properties-panel", visible=True) as properties_panel:
            gr.Markdown("### 📊 Scene Properties")
            
            # Object list
            self.ui_components['object_list'] = gr.Dataframe(
                headers=["Name", "Type", "Position"],
                datatype=["str", "str", "str"],
                label="Scene Objects",
                interactive=False
            )
            
            # Selected object properties
            with gr.Accordion("Object Properties", open=True):
                self.ui_components['object_name'] = gr.Textbox(
                    label="Name", interactive=True
                )
                self.ui_components['object_position'] = gr.Textbox(
                    label="Position (x,y,z)", value="0,0,0"
                )
                self.ui_components['object_material'] = gr.Dropdown(
                    choices=["Default", "Metal", "Glass", "Plastic"],
                    value="Default",
                    label="Material"
                )
            
        self.ui_components['properties_panel'] = properties_panel
        
        # Command Panel (Bottom Center)
        with gr.Column(elem_classes="overlay-panel command-panel", visible=True) as command_panel:
            gr.Markdown("### 💬 Natural Language Commands")
            
            self.ui_components['command_input'] = gr.Textbox(
                label="",
                placeholder="Type a command: 'place a hot cube at the center', 'add wind from left', 'drop a sphere from height 2m'...",
                lines=2,
                max_lines=3,
                show_label=False
            )
            
            with gr.Row():
                self.ui_components['execute_btn'] = gr.Button("Execute", variant="primary")
                self.ui_components['clear_btn'] = gr.Button("Clear Scene")
                
        self.ui_components['command_panel'] = command_panel
        
        # Timeline Panel (Bottom)
        with gr.Column(elem_classes="overlay-panel timeline-panel", visible=True) as timeline_panel:
            gr.Markdown("### ⏱️ Timeline")
            
            self.ui_components['timeline_slider'] = gr.Slider(
                minimum=0, maximum=100, value=0, step=1,
                label="Frame", show_label=False
            )
            
            with gr.Row():
                self.ui_components['speed_slider'] = gr.Slider(
                    minimum=0.1, maximum=5.0, value=1.0, step=0.1,
                    label="Speed", scale=2
                )
                self.ui_components['frame_info'] = gr.Textbox(
                    value="Frame: 0 / 0", label="", show_label=False, 
                    interactive=False, scale=1
                )
                
        self.ui_components['timeline_panel'] = timeline_panel
    
    def _setup_event_handlers(self):
        """Set up event handlers for the interface."""
        # Command execution
        self.ui_components['execute_btn'].click(
            self._execute_command,
            inputs=[self.ui_components['command_input']],
            outputs=[
                self.ui_components['viewport'],
                self.ui_components['object_list'],
                self.ui_components['frame_info']
            ]
        )
        
        # Simulation controls
        self.ui_components['play_btn'].click(
            self._play_simulation,
            outputs=[self.ui_components['viewport']]
        )
        
        self.ui_components['pause_btn'].click(
            self._pause_simulation
        )
        
        self.ui_components['reset_btn'].click(
            self._reset_simulation,
            outputs=[
                self.ui_components['viewport'],
                self.ui_components['object_list'],
                self.ui_components['timeline_slider'],
                self.ui_components['frame_info']
            ]
        )
        
        # Timeline control
        self.ui_components['timeline_slider'].change(
            self._update_frame,
            inputs=[self.ui_components['timeline_slider']],
            outputs=[self.ui_components['viewport'], self.ui_components['frame_info']]
        )
    
    def _execute_command(self, command: str) -> Tuple[go.Figure, List[List[str]], str]:
        """Execute a natural language command."""
        try:
            # Parse and execute command
            if self.simulation_engine:
                result = self.simulation_engine.execute_command(command)
            else:
                result = {"success": False, "error": "No simulation engine available"}
            
            # Create 3D visualization
            fig = self._create_3d_scene()
            
            # Update object list
            objects = self._get_scene_objects()
            
            # Update frame info
            frame_info = f"Frame: {self.current_frame} / {len(self.animation_frames)}"
            
            return fig, objects, frame_info
            
        except Exception as e:
            # Return error visualization
            fig = self._create_error_scene(str(e))
            return fig, [], f"Error: {str(e)}"
    
    def _create_3d_scene(self) -> go.Figure:
        """Create the main 3D scene visualization."""
        fig = go.Figure()
        
        # Set up 3D scene with origin at (0,0,0)
        fig.update_layout(
            scene=dict(
                xaxis=dict(range=[-5, 5], title="X"),
                yaxis=dict(range=[-5, 5], title="Y"),
                zaxis=dict(range=[-5, 5], title="Z"),
                camera=dict(
                    eye=dict(x=1.5, y=1.5, z=1.5),
                    center=dict(x=0, y=0, z=0)
                ),
                aspectmode='cube'
            ),
            margin=dict(l=0, r=0, t=0, b=0),
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
            height=800
        )
        
        # Add coordinate system origin
        fig.add_trace(go.Scatter3d(
            x=[0], y=[0], z=[0],
            mode='markers',
            marker=dict(size=8, color='red'),
            name='Origin (0,0,0)',
            showlegend=True
        ))
        
        # Add coordinate axes
        # X-axis (red)
        fig.add_trace(go.Scatter3d(
            x=[0, 1], y=[0, 0], z=[0, 0],
            mode='lines',
            line=dict(color='red', width=4),
            name='X-axis',
            showlegend=False
        ))
        
        # Y-axis (green)
        fig.add_trace(go.Scatter3d(
            x=[0, 0], y=[0, 1], z=[0, 0],
            mode='lines',
            line=dict(color='green', width=4),
            name='Y-axis',
            showlegend=False
        ))
        
        # Z-axis (blue)
        fig.add_trace(go.Scatter3d(
            x=[0, 0], y=[0, 0], z=[0, 1],
            mode='lines',
            line=dict(color='blue', width=4),
            name='Z-axis',
            showlegend=False
        ))
        
        return fig

    def _create_error_scene(self, error_msg: str) -> go.Figure:
        """Create an error visualization scene."""
        fig = go.Figure()

        fig.update_layout(
            scene=dict(
                xaxis=dict(range=[-1, 1]),
                yaxis=dict(range=[-1, 1]),
                zaxis=dict(range=[-1, 1]),
            ),
            title=f"Error: {error_msg}",
            margin=dict(l=0, r=0, t=40, b=0),
            height=800
        )

        return fig

    def _get_scene_objects(self) -> List[List[str]]:
        """Get list of objects in the scene."""
        objects = []

        # Get objects from simulation engine
        if hasattr(self.simulation_engine, 'scene') and self.simulation_engine.scene:
            for obj_id, obj in self.simulation_engine.scene.objects.items():
                position = f"{obj.transform.position.x:.2f},{obj.transform.position.y:.2f},{obj.transform.position.z:.2f}"
                objects.append([obj.name, obj.__class__.__name__, position])

        return objects

    def _play_simulation(self) -> go.Figure:
        """Start playing the simulation."""
        self.is_playing = True
        # Implementation for animation playback
        return self._create_3d_scene()

    def _pause_simulation(self):
        """Pause the simulation."""
        self.is_playing = False

    def _reset_simulation(self) -> Tuple[go.Figure, List[List[str]], gr.Slider, str]:
        """Reset the simulation to initial state."""
        self.current_frame = 0
        self.animation_frames = []
        self.is_playing = False

        # Reset simulation engine
        if hasattr(self.simulation_engine, 'reset'):
            self.simulation_engine.reset()

        fig = self._create_3d_scene()
        objects = self._get_scene_objects()
        timeline_update = gr.Slider(value=0, maximum=0)
        frame_info = "Frame: 0 / 0"

        return fig, objects, timeline_update, frame_info

    def _update_frame(self, frame_num: int) -> Tuple[go.Figure, str]:
        """Update the visualization to show a specific frame."""
        self.current_frame = frame_num

        # Update visualization based on frame
        fig = self._create_3d_scene()
        frame_info = f"Frame: {frame_num} / {len(self.animation_frames)}"

        return fig, frame_info

    def launch(self, **kwargs):
        """Launch the full-screen interface."""
        if not hasattr(self, 'app'):
            self.create_interface()

        return self.app.launch(**kwargs)
