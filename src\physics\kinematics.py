"""
Kinematics physics engine for rigid body dynamics.

Supports:
- Rigid bodies with mass, velocity, acceleration
- Gravity and external forces
- Collision detection and response
- Terrain and static objects
- Rolling, sliding, bouncing
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field

from .base import (
    PhysicsEngine, PhysicsObject, PhysicsDomain, Vector3D, Transform3D,
    PhysicsEngineRegistry, calculate_distance
)


@dataclass
class Material:
    """Material properties for physics objects."""
    density: float = 1.0  # kg/m³
    friction: float = 0.5  # coefficient of friction
    restitution: float = 0.3  # bounciness (0 = no bounce, 1 = perfect bounce)
    name: str = "default"


class RigidBody(PhysicsObject):
    """Rigid body with kinematics properties."""
    
    def __init__(self, 
                 name: str,
                 mass: float = 1.0,
                 shape: str = "sphere",
                 size: Vector3D = None,
                 material: Material = None,
                 **kwargs):
        super().__init__(name, **kwargs)
        
        self.mass = mass
        self.shape = shape  # "sphere", "box", "cylinder"
        self.size = size or Vector3D(1, 1, 1)  # radius for sphere, dimensions for box
        self.material = material or Material()
        
        # Kinematics state
        self.velocity = Vector3D()
        self.acceleration = Vector3D()
        self.angular_velocity = Vector3D()
        self.angular_acceleration = Vector3D()
        
        # Forces
        self.forces = Vector3D()  # Net force
        self.torques = Vector3D()  # Net torque
        
        # Collision properties
        self.is_static = False  # Static objects don't move
        self.collision_enabled = True
        
        # Analytics
        self.properties.update({
            'mass': mass,
            'velocity_magnitude': 0.0,
            'acceleration_magnitude': 0.0,
            'kinetic_energy': 0.0,
            'potential_energy': 0.0
        })
    
    def apply_force(self, force: Vector3D):
        """Apply force to the object."""
        self.forces = self.forces + force
    
    def apply_impulse(self, impulse: Vector3D):
        """Apply instantaneous impulse (change in momentum)."""
        if self.mass > 0:
            velocity_change = impulse * (1.0 / self.mass)
            self.velocity = self.velocity + velocity_change
    
    def update(self, dt: float, world_state: Dict[str, Any]):
        """Update rigid body physics."""
        if self.is_static or not self.active:
            return
        
        # Get gravity from world
        gravity = world_state['global_params'].get('gravity', Vector3D(0, -9.81, 0))
        
        # Apply gravity
        gravitational_force = gravity * self.mass
        self.apply_force(gravitational_force)
        
        # Update acceleration (F = ma)
        if self.mass > 0:
            self.acceleration = self.forces * (1.0 / self.mass)
        
        # Update velocity (v = v0 + at)
        self.velocity = self.velocity + self.acceleration * dt
        
        # Update position (x = x0 + vt)
        displacement = self.velocity * dt
        self.transform.position = self.transform.position + displacement
        
        # Update analytics
        self.properties.update({
            'velocity_magnitude': self.velocity.magnitude(),
            'acceleration_magnitude': self.acceleration.magnitude(),
            'kinetic_energy': 0.5 * self.mass * self.velocity.magnitude()**2,
            'potential_energy': self.mass * abs(gravity.y) * self.transform.position.y
        })
        
        # Reset forces for next frame
        self.forces = Vector3D()
        self.torques = Vector3D()
    
    def get_state(self) -> Dict[str, Any]:
        """Get current state."""
        return {
            'transform': {
                'position': self.transform.position.to_array(),
                'rotation': self.transform.rotation.to_array(),
                'scale': self.transform.scale.to_array()
            },
            'velocity': self.velocity.to_array(),
            'acceleration': self.acceleration.to_array(),
            'mass': self.mass,
            'shape': self.shape,
            'size': self.size.to_array(),
            'material': {
                'density': self.material.density,
                'friction': self.material.friction,
                'restitution': self.material.restitution
            },
            'properties': self.properties.copy()
        }
    
    def set_state(self, state: Dict[str, Any]):
        """Set object state."""
        if 'transform' in state:
            t = state['transform']
            self.transform.position = Vector3D.from_array(t['position'])
            self.transform.rotation = Vector3D.from_array(t['rotation'])
            self.transform.scale = Vector3D.from_array(t['scale'])
        
        if 'velocity' in state:
            self.velocity = Vector3D.from_array(state['velocity'])
        
        if 'acceleration' in state:
            self.acceleration = Vector3D.from_array(state['acceleration'])


class Terrain(PhysicsObject):
    """Terrain/ground object for collisions."""
    
    def __init__(self, 
                 name: str = "terrain",
                 height_function=None,
                 material: Material = None,
                 **kwargs):
        super().__init__(name, **kwargs)
        
        self.height_function = height_function or self._default_height
        self.material = material or Material(friction=0.7, restitution=0.1)
        self.is_static = True
        
    def _default_height(self, x: float, z: float) -> float:
        """Default flat ground at y=0."""
        return 0.0
    
    def get_height_at(self, x: float, z: float) -> float:
        """Get terrain height at given x,z coordinates."""
        return self.height_function(x, z)
    
    def get_normal_at(self, x: float, z: float) -> Vector3D:
        """Get terrain normal at given position (for collision response)."""
        # Simple approximation using finite differences
        epsilon = 0.01
        h_center = self.get_height_at(x, z)
        h_x = self.get_height_at(x + epsilon, z)
        h_z = self.get_height_at(x, z + epsilon)
        
        # Calculate gradient
        dx = (h_x - h_center) / epsilon
        dz = (h_z - h_center) / epsilon
        
        # Normal vector (pointing up)
        normal = Vector3D(-dx, 1.0, -dz)
        return normal.normalize()
    
    def update(self, dt: float, world_state: Dict[str, Any]):
        """Terrain doesn't update."""
        pass
    
    def get_state(self) -> Dict[str, Any]:
        return {
            'type': 'terrain',
            'material': {
                'friction': self.material.friction,
                'restitution': self.material.restitution
            }
        }
    
    def set_state(self, state: Dict[str, Any]):
        pass


class KinematicsEngine(PhysicsEngine):
    """Physics engine for kinematics simulation."""
    
    def __init__(self):
        super().__init__(PhysicsDomain.KINEMATICS)
        
        # Set default parameters
        self.world.set_global_param('gravity', Vector3D(0, -9.81, 0))
        self.world.set_global_param('air_resistance', 0.01)
        self.world.set_global_param('ground_level', 0.0)
        
        # Collision detection
        self.collision_enabled = True
        self.collision_tolerance = 0.01
        
    def step(self) -> bool:
        """Perform one simulation step."""
        if not self.running:
            return False
        
        # Update all objects
        world_state = self.world.get_world_state()
        
        for obj in self.world.objects.values():
            if obj.active:
                obj.update(self.world.dt, world_state)
        
        # Handle collisions
        if self.collision_enabled:
            self._handle_collisions()
        
        # Update world time
        self.world.time += self.world.dt
        
        # Collect analytics
        self.world.collect_analytics()
        
        return True
    
    def _handle_collisions(self):
        """Handle collisions between objects and terrain."""
        rigid_bodies = [obj for obj in self.world.objects.values() 
                       if isinstance(obj, RigidBody) and not obj.is_static]
        
        terrains = [obj for obj in self.world.objects.values() 
                   if isinstance(obj, Terrain)]
        
        # Check rigid body vs terrain collisions
        for body in rigid_bodies:
            for terrain in terrains:
                self._check_terrain_collision(body, terrain)
        
        # Check rigid body vs rigid body collisions
        for i, body1 in enumerate(rigid_bodies):
            for body2 in rigid_bodies[i+1:]:
                self._check_body_collision(body1, body2)
    
    def _check_terrain_collision(self, body: RigidBody, terrain: Terrain):
        """Check and resolve collision between rigid body and terrain."""
        pos = body.transform.position
        
        # Get terrain height at body position
        terrain_height = terrain.get_height_at(pos.x, pos.z)
        
        # Check if body is below terrain (simple sphere collision)
        if body.shape == "sphere":
            radius = body.size.x  # Assume size.x is radius for sphere
            if pos.y - radius <= terrain_height + self.collision_tolerance:
                # Collision detected
                self._resolve_terrain_collision(body, terrain, terrain_height)
    
    def _resolve_terrain_collision(self, body: RigidBody, terrain: Terrain, terrain_height: float):
        """Resolve collision between body and terrain."""
        pos = body.transform.position
        
        if body.shape == "sphere":
            radius = body.size.x
            
            # Move body above terrain
            new_y = terrain_height + radius + self.collision_tolerance
            body.transform.position = Vector3D(pos.x, new_y, pos.z)
            
            # Get terrain normal
            normal = terrain.get_normal_at(pos.x, pos.z)
            
            # Calculate relative velocity
            relative_velocity = body.velocity
            
            # Calculate impulse for collision response
            velocity_along_normal = (
                relative_velocity.x * normal.x + 
                relative_velocity.y * normal.y + 
                relative_velocity.z * normal.z
            )
            
            if velocity_along_normal < 0:  # Objects are separating
                return
            
            # Apply restitution
            restitution = min(body.material.restitution, terrain.material.restitution)
            impulse_magnitude = -(1 + restitution) * velocity_along_normal
            
            # Apply impulse
            impulse = normal * impulse_magnitude
            body.apply_impulse(impulse)
            
            # Apply friction
            friction = (body.material.friction + terrain.material.friction) / 2
            tangent_velocity = relative_velocity - normal * velocity_along_normal
            if tangent_velocity.magnitude() > 0:
                friction_impulse = tangent_velocity.normalize() * (-friction * impulse_magnitude)
                body.apply_impulse(friction_impulse)
    
    def _check_body_collision(self, body1: RigidBody, body2: RigidBody):
        """Check collision between two rigid bodies."""
        # Simple sphere-sphere collision for now
        if body1.shape == "sphere" and body2.shape == "sphere":
            distance = calculate_distance(body1.transform.position, body2.transform.position)
            min_distance = body1.size.x + body2.size.x  # sum of radii
            
            if distance < min_distance + self.collision_tolerance:
                self._resolve_body_collision(body1, body2)
    
    def _resolve_body_collision(self, body1: RigidBody, body2: RigidBody):
        """Resolve collision between two rigid bodies."""
        # Implement elastic collision response
        # This is a simplified version - real implementation would be more complex
        pass
    
    def reset(self):
        """Reset simulation."""
        self.world.time = 0.0
        self.world.analytics_data.clear()
        
        # Reset all objects to initial state
        for obj in self.world.objects.values():
            if isinstance(obj, RigidBody):
                obj.velocity = Vector3D()
                obj.acceleration = Vector3D()
                obj.forces = Vector3D()
    
    def create_sloped_hill(self, height: float, slope_angle: float = 30.0) -> Terrain:
        """Create a sloped hill terrain."""
        def hill_height(x: float, z: float) -> float:
            # Simple linear slope
            slope_rad = np.radians(slope_angle)
            return max(0, height - x * np.tan(slope_rad))
        
        terrain = Terrain(
            name="sloped_hill",
            height_function=hill_height,
            material=Material(friction=0.3, restitution=0.1)
        )
        
        self.world.add_object(terrain)
        return terrain
    
    def create_ball(self, mass: float, radius: float, position: Vector3D) -> RigidBody:
        """Create a ball (sphere) rigid body."""
        ball = RigidBody(
            name="ball",
            mass=mass,
            shape="sphere",
            size=Vector3D(radius, radius, radius),
            material=Material(friction=0.4, restitution=0.6),
            transform=Transform3D(position=position)
        )
        
        self.world.add_object(ball)
        return ball


# Register the kinematics engine
PhysicsEngineRegistry.register(PhysicsDomain.KINEMATICS, KinematicsEngine)
