#!/usr/bin/env python3
"""
Quick test of the 3D Generative Physics Sandbox core functionality.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.physics.heat_diffusion import HeatDiffusion3D
from src.visualization.basic_viz import plot_2d_slices


def test_physics_simulation():
    """Test the physics simulation with visualization."""
    print("🔥 Testing 3D Heat Diffusion Physics")
    print("=" * 40)
    
    # Create solver with smaller grid for faster computation
    solver = HeatDiffusion3D(grid_size=(16, 16, 16), dt=0.02, alpha=0.15)
    
    # Add heat sources based on natural language-like commands
    print("Adding heat sources:")
    print("- 'Hot cube in center' -> cube at (8,8,8)")
    solver.add_heat_source((8, 8, 8), shape="cube", size=2, temperature=100.0)
    
    print("- 'Warm sphere on left' -> sphere at (4,8,8)")
    solver.add_heat_source((4, 8, 8), shape="sphere", size=1, temperature=60.0)
    
    print("- 'Cool spot on right' -> cube at (12,8,8)")
    solver.add_heat_source((12, 8, 8), shape="cube", size=1, temperature=20.0)
    
    # Show initial state
    print(f"\nInitial state:")
    print(f"  Max temperature: {solver.temperature.max():.1f}°")
    print(f"  Min temperature: {solver.temperature.min():.1f}°")
    print(f"  Total heat: {solver.temperature.sum():.0f}")
    
    # Run simulation
    print(f"\nRunning simulation for 20 steps...")
    history = solver.simulate(20)
    
    # Show final state
    print(f"\nFinal state:")
    print(f"  Max temperature: {history[-1].max():.1f}°")
    print(f"  Min temperature: {history[-1].min():.1f}°")
    print(f"  Total heat: {history[-1].sum():.0f}")
    
    # Create visualizations
    print(f"\nCreating visualizations...")
    
    # Plot initial state
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.imshow(history[0][:, :, 8], cmap='hot', origin='lower')
    plt.title('Initial State (t=0)')
    plt.colorbar()
    
    plt.subplot(1, 3, 2)
    plt.imshow(history[10][:, :, 8], cmap='hot', origin='lower')
    plt.title('Middle State (t=10)')
    plt.colorbar()
    
    plt.subplot(1, 3, 3)
    plt.imshow(history[-1][:, :, 8], cmap='hot', origin='lower')
    plt.title('Final State (t=20)')
    plt.colorbar()
    
    plt.tight_layout()
    plt.savefig('physics_simulation_test.png', dpi=150, bbox_inches='tight')
    print("Saved visualization to 'physics_simulation_test.png'")
    
    # Show temperature evolution over time
    max_temps = [frame.max() for frame in history]
    avg_temps = [frame.mean() for frame in history]
    
    plt.figure(figsize=(10, 6))
    plt.plot(max_temps, 'r-', label='Max Temperature', linewidth=2)
    plt.plot(avg_temps, 'b-', label='Average Temperature', linewidth=2)
    plt.xlabel('Time Step')
    plt.ylabel('Temperature')
    plt.title('Temperature Evolution Over Time')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('temperature_evolution.png', dpi=150, bbox_inches='tight')
    print("Saved temperature evolution to 'temperature_evolution.png'")
    
    return history


def test_language_parsing():
    """Test basic language-to-action parsing."""
    print("\n🗣️  Testing Natural Language Interface")
    print("=" * 40)
    
    # Simple hardcoded parser for demo
    commands = [
        "place a hot cube in the center",
        "add a warm sphere on the left side", 
        "create a cold region at the top",
        "drop a burning cube at position 5,5,5"
    ]
    
    for cmd in commands:
        print(f"Command: '{cmd}'")
        
        # Simple keyword-based parsing
        if "hot" in cmd and "cube" in cmd and "center" in cmd:
            action = {"type": "add_heat", "shape": "cube", "pos": (8,8,8), "temp": 100}
        elif "warm" in cmd and "sphere" in cmd and "left" in cmd:
            action = {"type": "add_heat", "shape": "sphere", "pos": (3,8,8), "temp": 60}
        elif "cold" in cmd and "top" in cmd:
            action = {"type": "add_heat", "shape": "cube", "pos": (8,8,13), "temp": 10}
        elif "burning" in cmd and "5,5,5" in cmd:
            action = {"type": "add_heat", "shape": "cube", "pos": (5,5,5), "temp": 150}
        else:
            action = {"type": "unknown"}
        
        print(f"  -> Parsed action: {action}")
        print()


def test_model_architecture():
    """Test the ML model architecture."""
    print("🧠 Testing 3D U-Net Model Architecture")
    print("=" * 40)
    
    try:
        import torch
        from src.models.unet3d import PhysicsPredictor
        
        # Create model
        model = PhysicsPredictor(grid_size=(16, 16, 16))
        n_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"Model created successfully!")
        print(f"Parameters: {n_params:,}")
        
        # Test forward pass
        test_input = torch.randn(1, 16, 16, 16)
        with torch.no_grad():
            output = model(test_input)
        
        print(f"Input shape: {test_input.shape}")
        print(f"Output shape: {output.shape}")
        print(f"Forward pass successful!")
        
        return True
        
    except Exception as e:
        print(f"Model test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 NeoPhysics: 3D Generative Physics Sandbox")
    print("Quick Functionality Test")
    print("=" * 50)
    
    # Test 1: Physics simulation
    history = test_physics_simulation()
    
    # Test 2: Language parsing
    test_language_parsing()
    
    # Test 3: Model architecture
    model_ok = test_model_architecture()
    
    # Summary
    print("\n✅ Test Summary")
    print("=" * 40)
    print("✓ Physics simulation: Working")
    print("✓ Heat diffusion solver: Working") 
    print("✓ Visualization: Working")
    print("✓ Language parsing: Basic implementation")
    print(f"✓ ML model: {'Working' if model_ok else 'Failed'}")
    
    print(f"\n🎯 Next Steps:")
    print("1. Train the 3D U-Net model on physics data")
    print("2. Implement advanced language parsing (LLM integration)")
    print("3. Create web interface with Gradio")
    print("4. Add WebGL 3D visualization")
    print("5. Expand to other physics domains (fluids, rigid bodies)")
    
    print(f"\n📁 Generated files:")
    print("- physics_simulation_test.png")
    print("- temperature_evolution.png")


if __name__ == "__main__":
    main()
