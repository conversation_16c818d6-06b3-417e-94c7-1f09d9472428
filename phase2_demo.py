#!/usr/bin/env python3
"""
Phase 2 Demo: Advanced Natural Language Physics Sandbox

This demo showcases the complete Phase 2 functionality:
- Extensible physics architecture supporting multiple domains
- Advanced natural language parsing (keyword + LLM hybrid)
- 3D scene management with Blender-like capabilities
- Web-based UI with real-time visualization
- Complete integration of all components

Example usage:
    python phase2_demo.py --ui web          # Launch web interface
    python phase2_demo.py --ui console     # Console interface
    python phase2_demo.py --demo           # Run automated demo
"""

import os
import sys
import argparse
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.simulation_engine import SimulationEngine
from src.physics.base import PhysicsDomain
from src.visualization.blender_ui import BlenderLikeUI


def setup_logging(level=logging.INFO):
    """Setup logging configuration."""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('neophysics.log')
        ]
    )


def run_automated_demo():
    """Run automated demonstration of capabilities."""
    print("🚀 NeoPhysics Phase 2 - Automated Demo")
    print("=" * 50)
    
    # Create simulation engine with hybrid parser
    print("Initializing simulation engine...")
    engine = SimulationEngine(
        default_domain=PhysicsDomain.KINEMATICS,
        parser_type="hybrid",  # Uses both keyword and LLM parsing
        use_llm_fallback=True
    )
    
    # Demo commands showcasing natural language understanding
    demo_commands = [
        # Basic object creation
        "create a sloped hill that's 10 meters high and place a 5kg ball at the top",
        
        # Physics parameter adjustment
        "set gravity to 9.8 m/s²",
        
        # More complex object creation
        "add a bouncy rubber ball with mass 2kg at position 5,0,5",
        
        # Create terrain features
        "make a steep ramp with 45 degree slope",
        
        # Simulation control
        "run simulation for 5 seconds",
        
        # Data analysis
        "analyze the velocity and acceleration of all objects",
        
        # Reset for next demo
        "reset the simulation"
    ]
    
    print(f"\nExecuting {len(demo_commands)} natural language commands...")
    print("-" * 50)
    
    for i, command in enumerate(demo_commands, 1):
        print(f"\n[{i}/{len(demo_commands)}] Command: '{command}'")
        
        try:
            results = engine.execute_command(command)
            
            for result in results:
                if result.get('success'):
                    print(f"  ✅ {result.get('message', 'Success')}")
                    if 'object_id' in result:
                        print(f"     Created: {result['object_type']} (ID: {result['object_id'][:8]}...)")
                else:
                    print(f"  ❌ {result.get('error', 'Failed')}")
                    
        except Exception as e:
            print(f"  ❌ Exception: {e}")
        
        # Show current scene state
        if i % 3 == 0:  # Every 3 commands
            analytics = engine.get_analytics()
            print(f"  📊 Scene: {analytics['object_count']} objects, "
                  f"Time: {analytics['simulation_time']:.2f}s")
    
    print("\n" + "=" * 50)
    print("✅ Automated demo completed successfully!")
    
    # Show final analytics
    final_analytics = engine.get_analytics()
    print(f"\nFinal State:")
    print(f"  Objects in scene: {final_analytics['object_count']}")
    print(f"  Simulation time: {final_analytics['simulation_time']:.2f}s")
    print(f"  Physics domain: {final_analytics['physics_domain']}")
    print(f"  Commands executed: {len(final_analytics['command_history'])}")
    
    return engine


def run_console_interface():
    """Run interactive console interface."""
    print("🎮 NeoPhysics Phase 2 - Console Interface")
    print("=" * 50)
    print("Type natural language commands to control the physics simulation.")
    print("Examples:")
    print("  - 'create a ball with mass 5kg at the top of a hill'")
    print("  - 'set gravity to 12 m/s²'")
    print("  - 'run simulation for 3 seconds'")
    print("  - 'analyze object velocities'")
    print("Type 'help' for more commands, 'quit' to exit.")
    print("-" * 50)
    
    # Create simulation engine
    engine = SimulationEngine(
        default_domain=PhysicsDomain.KINEMATICS,
        parser_type="hybrid"
    )
    
    while True:
        try:
            # Get user input
            command = input("\n🔥 NeoPhysics> ").strip()
            
            if not command:
                continue
            
            # Handle special commands
            if command.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            elif command.lower() == 'help':
                print_help()
                continue
            
            elif command.lower() == 'status':
                print_status(engine)
                continue
            
            elif command.lower() == 'clear':
                os.system('cls' if os.name == 'nt' else 'clear')
                continue
            
            # Execute physics command
            print(f"Executing: {command}")
            results = engine.execute_command(command)
            
            # Display results
            for result in results:
                if result.get('success'):
                    print(f"✅ {result.get('message', 'Command executed successfully')}")
                else:
                    print(f"❌ {result.get('error', 'Command failed')}")
            
            # Show brief status
            analytics = engine.get_analytics()
            print(f"📊 Scene: {analytics['object_count']} objects, "
                  f"Time: {analytics['simulation_time']:.2f}s")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


def print_help():
    """Print help information."""
    help_text = """
🔥 NeoPhysics Commands:

Object Creation:
  - create a ball/sphere with mass X kg at position Y,Z,W
  - make a cube/box with size X,Y,Z at position A,B,C
  - build a hill/ramp with height X meters and slope Y degrees

Physics Parameters:
  - set gravity to X m/s²
  - change air resistance to X
  - adjust time scale to X

Simulation Control:
  - run simulation for X seconds
  - start/stop/pause simulation
  - reset simulation

Analysis:
  - analyze object data
  - show velocity/acceleration of objects
  - get simulation statistics

Special Commands:
  - help: show this help
  - status: show current simulation status
  - clear: clear screen
  - quit/exit: exit program
"""
    print(help_text)


def print_status(engine):
    """Print current simulation status."""
    analytics = engine.get_analytics()
    
    print("\n📊 Current Simulation Status:")
    print(f"  Physics Domain: {analytics['physics_domain']}")
    print(f"  Objects in Scene: {analytics['object_count']}")
    print(f"  Simulation Time: {analytics['simulation_time']:.2f}s")
    print(f"  Engine Running: {engine.running}")
    print(f"  Engine Paused: {engine.paused}")
    
    if analytics['object_count'] > 0:
        print("\n  Scene Objects:")
        # This would show object details if available
        print("    (Object details would be shown here)")


def run_web_interface():
    """Launch web-based Blender-like interface."""
    print("🌐 Launching NeoPhysics Web Interface...")
    print("=" * 50)
    
    # Create simulation engine
    engine = SimulationEngine(
        default_domain=PhysicsDomain.KINEMATICS,
        parser_type="hybrid"
    )
    
    # Create and launch UI
    ui = BlenderLikeUI(engine)
    
    print("🚀 Starting web server...")
    print("📱 Interface will open in your browser")
    print("🔗 URL: http://localhost:7860")
    print("⏹️  Press Ctrl+C to stop")
    
    try:
        ui.launch(
            share=False,
            server_name="0.0.0.0",
            server_port=7860,
            show_error=True,
            quiet=False
        )
    except KeyboardInterrupt:
        print("\n👋 Web interface stopped")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="NeoPhysics Phase 2 Demo",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python phase2_demo.py --demo           # Run automated demo
  python phase2_demo.py --ui console     # Interactive console
  python phase2_demo.py --ui web         # Web interface (Blender-like)
  python phase2_demo.py --ui web --debug # Web interface with debug logging
        """
    )
    
    parser.add_argument(
        '--ui', 
        choices=['console', 'web'], 
        default='console',
        help='User interface type'
    )
    
    parser.add_argument(
        '--demo', 
        action='store_true',
        help='Run automated demonstration'
    )
    
    parser.add_argument(
        '--debug', 
        action='store_true',
        help='Enable debug logging'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    setup_logging(log_level)
    
    # Create directories
    os.makedirs("data", exist_ok=True)
    os.makedirs("models", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    try:
        if args.demo:
            run_automated_demo()
        elif args.ui == 'console':
            run_console_interface()
        elif args.ui == 'web':
            run_web_interface()
        else:
            parser.print_help()
    
    except Exception as e:
        logging.error(f"Application error: {e}")
        print(f"❌ Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
