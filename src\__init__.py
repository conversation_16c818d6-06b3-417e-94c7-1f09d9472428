# NeoPhysics: 3D Generative Physics Sandbox

from .core.simulation_engine import SimulationEngine
from .physics.base import PhysicsDomain, PhysicsEngineRegistry
from .language.parser import NLPParser, PhysicsCommand
from .language.llm_parser import create_parser
from .visualization.blender_ui import BlenderLikeUI

__version__ = "0.2.0"
__author__ = "NeoPhysics Team"
__description__ = "3D Generative Physics Sandbox with Natural Language Control"
